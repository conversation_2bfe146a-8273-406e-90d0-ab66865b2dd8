# Cloud Storage Module

## 架构概述

这个云存储模块采用**条件导入**架构，根据部署环境自动选择最优的存储实现：

```text
src/lib/cloud-storage/
├── types.ts              # 统一接口定义
├── index.ts              # 条件导入入口
├── cloud-storage-aws.ts  # AWS SDK 实现 (Node.js 优化)
├── cloud-storage-cf.ts   # aws4fetch 实现 (Edge Runtime 优化)
└── README.md            # 本文档
```

## 环境自动选择

| 环境 | 实现 | 依赖 | 打包体积 | 性能 |
|------|------|------|----------|------|
| **Node.js** | `cloud-storage-aws.ts` | AWS SDK v3 | ~580KB | 最优 |
| **Cloudflare Workers** | `cloud-storage-cf.ts` | aws4fetch | ~25KB | 良好 |

## 使用方法

### 1. 基本导入

```typescript
import { 
  createPresignedPreviewUrl,
  createPresignedDownloadUrl,
  createPresignedUploadUrl,
  uploadFromUrl,
  uploadFromBuffer,
  deleteFiles,
  isOwnStorageUrl,
  verifyFileExists,
} from '@/lib/cloud-storage';
```

### 2. 类型导入

```typescript
import type { 
  StorageConfig,
  PresignedUploadResult,
  UploadResult,
  FileExistsResult,
} from '@/lib/cloud-storage';
```

### 3. 环境配置

通过环境变量控制实现选择：

```bash
# 强制使用 Cloudflare 实现
DEPLOYMENT_TARGET=cloudflare

# 强制使用 Node.js 实现  
DEPLOYMENT_TARGET=node

# 自动检测（默认）
# 不设置或设置为其他值时，根据运行时环境自动选择
```

## 接口一致性

所有实现都严格遵循 `CloudStorageProvider` 接口，确保：

- ✅ **API 完全一致** - 相同的函数签名和返回类型
- ✅ **类型安全** - TypeScript 编译时检查接口实现
- ✅ **无缝切换** - 无需修改调用代码即可切换实现
- ✅ **统一维护** - 接口变更时编译器会提示所有需要更新的地方

## 性能优化

### Node.js 环境优势

- 使用 AWS SDK v3 的 `Upload` 类，支持分片上传
- 原生 Node.js 流处理，内存效率高
- 完整的 AWS 生态系统支持

### Cloudflare Workers 优势

- 使用 aws4fetch，避免文件系统依赖
- 95% 更小的打包体积
- 针对 Edge Runtime 优化

## 开发指南

### 添加新功能

1. 在 `types.ts` 中添加接口定义
2. 在两个实现文件中添加相同的函数
3. TypeScript 会确保接口实现的一致性

### 测试

```bash
# 测试 Node.js 实现
DEPLOYMENT_TARGET=node npm test

# 测试 Cloudflare 实现
DEPLOYMENT_TARGET=cloudflare npm test
```

## 最佳实践

1. **优先使用条件导入入口** - 始终从 `index.ts` 导入
2. **避免直接导入实现** - 不要直接导入 `*-aws.ts` 或 `*-cf.ts`
3. **使用统一类型** - 从 `types.ts` 或入口文件导入类型
4. **环境变量配置** - 在部署时设置 `DEPLOYMENT_TARGET`

## AI 代码助手使用指南

### ✅ 正确的导入方式

```typescript
// 导入所有函数和类型都从主入口点
import { 
  createPresignedPreviewUrl,
  createPresignedDownloadUrl,
  createPresignedUploadUrl,
  uploadFromUrl,
  uploadFromBuffer,
  deleteFiles,
  isOwnStorageUrl,
  verifyFileExists,
} from '@/lib/cloud-storage';

// 导入类型也从主入口点
import type { 
  StorageConfig,
  PresignedUploadResult,
  UploadResult,
  FileExistsResult,
} from '@/lib/cloud-storage';
```

### ❌ 错误的导入方式

```typescript
// 不要从实现文件导入
import { ... } from '@/lib/cloud-storage/cloud-storage-aws';
import { ... } from '@/lib/cloud-storage/cloud-storage-cf';

// 不要从类型文件导入
import type { ... } from '@/lib/cloud-storage/types';
```

### 🤖 AI 助手注意事项

1. **始终使用主入口点** - 所有导入都应该从 `@/lib/cloud-storage` 进行
2. **不要直接访问实现文件** - 系统会自动选择最优实现
3. **保持导入一致性** - 函数和类型都从同一个入口点导入
4. **环境自动适配** - 无需手动判断环境，系统会自动处理

这个架构确保了代码的可维护性、类型安全性和部署灵活性，同时最大化了每个环境的性能优势。
