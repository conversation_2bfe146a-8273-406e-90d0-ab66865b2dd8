# 代码风格规范

## 基本原则
- **TypeScript 优先** - 所有代码使用 TypeScript，严格类型检查
- **英文注释** - 代码注释和日志使用英文，尽量详细
- **中文交流** - 问题讨论和解决方案阐述使用中文
- **简洁实用** - 避免过度工程化，优先选择简单解决方案

## TypeScript 规范

### 类型定义
- **Interface 优先** - 对象定义使用 interface 而非 type
- **常量对象** - 使用常量对象替代 enum
- **Drizzle类型** - 使用 `$inferSelect` 和 `$inferInsert` 推导类型
- **统一错误处理** - 使用 `Result<T>` 类型处理成功/失败状态

## React 组件规范
- **函数式组件** - 使用函数组件配合TypeScript
- **Props接口** - 组件名 + Props 命名规范
- **Hook命名** - use + 功能描述
- **事件处理** - handle + 动作名称

## 文件组织和命名

### 文件命名规范
- **组件文件** - PascalCase: `UserProfile.tsx`
- **Hook文件** - camelCase: `useAuth.ts`
- **工具文件** - camelCase: `apiUtils.ts`
- **类型文件** - camelCase: `user.types.ts`
- **数据库模型** - camelCase: `user.ts` (在 models/ 目录)
- **服务文件** - camelCase: `user.ts` (在 services/ 目录)

### 导入导出规范
- **分组导入** - 按层级分组：库 → 内部模块 → 组件
- **具名导出** - 优先使用具名导出
- **默认导出** - 仅用于页面组件

## 样式规范

### 样式规范
- **Tailwind优先** - 使用Tailwind CSS而非内联样式  
- **cn函数** - 使用 `cn()` 处理条件样式
- **响应式** - 移动端优先的响应式设计
- **主题支持** - 支持深色/浅色主题

## 性能优化

## 性能优化
- **React性能** - 合理使用 `useMemo`、`useCallback` 和 `React.memo`
- **查询优化** - 只选择需要的字段，使用 `limit` 限制结果
- **懒加载** - 适当使用动态导入和 `React.lazy`

## 错误处理和日志

## 错误处理和日志
- **统一错误处理** - 使用 `Result<T>` 类型处理成功/失败
- **结构化日志** - 包含上下文信息的日志格式
- **边界处理** - 在适当位置设置错误边界

## 代码质量

## 代码质量
- **英文注释** - 代码注释和日志使用英文，复杂参数和逻辑，要包含使用示例。
- **避免魔术数字** - 使用命名常量
- **环境变量** - 类型安全的配置管理
- **JSDoc** - 为公共API提供文档注释

参考文件：
- [src/db/schema.ts](mdc:src/db/schema.ts) - 数据库类型定义
- [src/components/ui/button.tsx](mdc:src/components/ui/button.tsx) - 组件样式示例
- [src/lib/utils.ts](mdc:src/lib/utils.ts) - 工具函数示例

