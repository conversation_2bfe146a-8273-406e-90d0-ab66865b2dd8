import { NextRequest, NextResponse } from "next/server";
import { createPresignedUploads } from "@/services/file";
import { respData, respErr, nextRespErr, nextRespData } from "@/lib/resp";
import logger from "@/lib/logger";
import {
  PresignedUploadRequest,
  PresignedUploadResponse,
  PresignedUploadInfo,
} from "@/types/file-upload";
import { getUserUuid } from "@/services/user";

/**
 * Generate presigned upload URLs for direct client-side file upload
 * Supports both single file upload (array length 1) and batch uploads
 * Client is responsible for all file validation and processing
 */
export async function POST(req: NextRequest) {
  try {
    // Extract user information from headers
    // const userUuid = req.headers.get('x-user-uuid');
    const userUuid = await getUserUuid();
    if (!userUuid) {
      return nextRespErr("User authentication required");
    }

    // Parse and validate request body
    let requestBody: PresignedUploadRequest;
    try {
      requestBody = await req.json();
    } catch (error) {
      return nextRespErr("Invalid JSON in request body");
    }

    const { filesInfo, fileCategory } = requestBody;

    // Basic validation only - trust client-side processing
    if (!filesInfo || !Array.isArray(filesInfo) || filesInfo.length === 0) {
      return nextRespErr("Files array is required and cannot be empty");
    }

    if (filesInfo.length > 10) {
      return nextRespErr("Maximum 10 files per batch upload");
    }

    // Minimal validation - only check data completeness
    for (let i = 0; i < filesInfo.length; i++) {
      const fileInfo = filesInfo[i];
      
      if (!fileInfo.fileName || typeof fileInfo.fileName !== 'string') {
        return nextRespErr(`File ${i + 1}: fileName is required and must be a string`);
      }

      if (!fileInfo.mimeType || typeof fileInfo.mimeType !== 'string') {
        return nextRespErr(`File ${i + 1}: mimeType is required and must be a string`);
      }

      if (!fileInfo.fileSize || typeof fileInfo.fileSize !== 'number' || fileInfo.fileSize <= 0) {
        return nextRespErr(`File ${i + 1}: fileSize is required and must be a positive number`);
      }

      // Client is responsible for all file type and size validation
      // No server-side limits enforced here
    }

    logger.info('Processing presigned upload request', {
      userUuid,
      fileCount: filesInfo.length,
      fileCategory,
      filesInfo
    }, { filePath: "app/api/files/upload/presigned/route.ts", functionName: 'POST' });

    // Create batch presigned upload URLs
    const presignedUploadInfos = await createPresignedUploads(
      filesInfo,
      fileCategory,
      userUuid,
    );

    const response: PresignedUploadResponse = {
      presignedUploadInfos,
    };

    logger.info('Successfully generated presigned upload URLs', {
      userUuid,
      fileCount: presignedUploadInfos.length,
      fileIds: presignedUploadInfos.map((p: PresignedUploadInfo) => p.fileId)
    }, { filePath: "app/api/files/upload/presigned/route.ts", functionName: 'POST' });

    return nextRespData(response);
  } catch (error) {
    logger.error('Failed to generate presigned upload URLs', error, {
      userUuid: await getUserUuid()
    }, { filePath: "app/api/files/upload/presigned/route.ts", functionName: 'POST' });
    
    return nextRespErr("Failed to generate upload URLs");
  }
}
