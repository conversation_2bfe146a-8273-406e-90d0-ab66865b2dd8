/**
 * URL Generation Service
 * 
 * This service handles the generation of URLs for content pages across different
 * languages and content types. It ensures consistent URL structure throughout
 * the application and provides a centralized place for URL-related logic.
 * 
 * Key Features:
 * - Consistent URL structure across all content types
 * - Locale-aware URL generation (English as default, no prefix)
 * - Support for both content detail pages and list pages
 * - Centralized URL pattern management
 * 
 * URL Structure:
 * - English (default): '/blogs/my-post', '/products/my-product'
 * - Other languages: '/zh/blogs/my-post', '/zh/products/my-product'
 * - List pages: '/blogs', '/zh/blogs'
 */

import type { ContentType } from './types'
import { getContentBasePath } from './content-detection'

/**
 * Generate URL for content in a specific locale
 * 
 * This function constructs the correct URL for accessing content in a specific
 * language, following the application's URL structure conventions. It handles
 * both individual content pages and list pages.
 * 
 * URL Structure Examples:
 * - English (default): '/blogs/my-post', '/products/my-product'
 * - Other languages: '/zh/blogs/my-post', '/zh/products/my-product'
 * - List pages: '/blogs', '/zh/blogs'
 * 
 * @param contentType - Type of content ('blog', 'product', 'case-study', 'other')
 * @param slug - Content identifier, empty string for list pages
 * @param locale - Target locale code (e.g., 'en', 'zh')
 * @returns Properly formatted URL string
 */
export function generateContentUrl(
  contentType: ContentType,
  slug: string,
  locale: string
): string {
  // Handle non-content pages (home, about, etc.)
  if (contentType === 'other') {
    return locale === 'en' ? '/' : `/${locale}`
  }

  // Get the base path for this content type
  const basePath = getContentBasePath(contentType)
  // English is the default locale and doesn't need a prefix
  const localePrefix = locale === 'en' ? '' : `/${locale}`

  // If no slug provided, return the list page URL
  if (!slug) {
    return `${localePrefix}${basePath}`
  }

  // Return the full content URL with locale prefix, base path, and slug
  return `${localePrefix}${basePath}/${slug}`
}

/**
 * Generate canonical URL for content
 * 
 * This function generates the canonical URL for a piece of content, which is
 * useful for SEO purposes and link sharing. The canonical URL always uses
 * the default locale (English) unless the content only exists in other languages.
 * 
 * @param contentType - Type of content
 * @param slug - Content identifier
 * @param availableLocales - List of locales where the content exists
 * @returns Canonical URL string
 */
export function generateCanonicalUrl(
  contentType: ContentType,
  slug: string,
  availableLocales: string[]
): string {
  // If content exists in English, use English as canonical
  if (availableLocales.includes('en')) {
    return generateContentUrl(contentType, slug, 'en')
  }
  
  // Otherwise, use the first available locale
  const firstLocale = availableLocales[0] || 'en'
  return generateContentUrl(contentType, slug, firstLocale)
}

/**
 * Generate alternate URLs for different languages
 * 
 * This function generates a map of alternate URLs for the same content in
 * different languages. This is useful for generating hreflang attributes
 * for SEO purposes.
 * 
 * @param contentType - Type of content
 * @param slug - Content identifier
 * @param availableLocales - List of locales where the content exists
 * @returns Map of locale to URL
 */
export function generateAlternateUrls(
  contentType: ContentType,
  slug: string,
  availableLocales: string[]
): Record<string, string> {
  const alternateUrls: Record<string, string> = {}
  
  for (const locale of availableLocales) {
    alternateUrls[locale] = generateContentUrl(contentType, slug, locale)
  }
  
  return alternateUrls
}
