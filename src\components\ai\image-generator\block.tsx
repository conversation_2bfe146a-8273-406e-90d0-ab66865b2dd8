"use client";

import ImageGeneratorCore from "@/components/ai/image-generator";
import { ImageGenerationMode } from "@/types/ai/image-gen-types";
import ImageGeneratorHeader from "./header";

interface ImageGeneratorBlockProps {
  id?: string;
  className?: string;
  showHeader?: boolean;
  showSubtitle?: boolean;
}

export default function ImageGeneratorBlock({
  id,
  className = "",
  showHeader = true,
  showSubtitle = true
}: ImageGeneratorBlockProps) {
  
  return (
    // To modify the spacing:
    // - "py-24" controls vertical padding (pt-24 for top, pb-24 for bottom)
    // - Change "pt-24" to adjust distance from page top to title
    // - Change "pb-24" to adjust distance at the bottom
    // Examples: "pt-12 pb-24" (less top space), "pt-32 pb-24" (more top space)
    <section id={id} className={`pt-24 pb-24 ${className}`}>
      <div className="container">
        {/* Header component - can be toggled with showHeader prop */}
        {showHeader && (
          <div className="mb-16">
            <ImageGeneratorHeader showSubtitle={showSubtitle} />
          </div>
        )}
        {/* Main image generator component */}
        <div className="mx-auto w-full max-w-5xl">
          <ImageGeneratorCore initialMode={ImageGenerationMode.TextToImage} />
        </div>
      </div>
    </section>
  );
}
