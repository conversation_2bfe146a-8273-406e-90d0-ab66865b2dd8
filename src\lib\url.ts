/**
 * URL validation result interface
 */
export interface UrlValidationResult {
  validUrls: string[];
  invalidUrls: string[];
  isAllValid: boolean;
}

/**
 * Validates the validity of an array of URLs
 * 
 * **Validation Content:**
 * - Protocol format (http://, https://, ftp://, etc.)
 * - Hostname format (domain names or IP addresses)
 * - Port numbers (if specified, validates as valid numbers)
 * - Path structure (URL path format)
 * - Query parameters (basic format of URL parameters)
 * - Fragment identifiers (anchor format after #)
 * 
 * **Validation Limitations:**
 * ⚠️ This function performs FORMAT validation only, does NOT include:
 * - Network connectivity testing (no HTTP requests sent)
 * - Server reachability verification
 * - Resource existence checking
 * - Permission and authentication validation
 * 
 * @param urls - Array of URLs to validate
 * @returns Validation result containing valid URLs, invalid URLs, and overall validity status
 */
export function validateUrls(urls: string[]): UrlValidationResult {
  if (!urls || !Array.isArray(urls)) {
    return {
      validUrls: [],
      invalidUrls: [],
      isAllValid: false,
    };
  }

  const validUrls: string[] = [];
  const invalidUrls: string[] = [];

  urls.forEach((url) => {
    try {
      new URL(url);
      validUrls.push(url);
    } catch {
      invalidUrls.push(url);
    }
  });

  return {
    validUrls,
    invalidUrls,
    isAllValid: validUrls.length === urls.length,
  };
}
