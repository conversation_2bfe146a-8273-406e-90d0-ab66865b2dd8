---
description: Database operations and Drizzle-ORM usage guidelines
globs: src/db/**,src/models/**
alwaysApply: false
---
# 数据库操作规范

## Drizzle-ORM 架构

本项目使用 Drizzle-ORM 作为数据库ORM，提供类型安全的数据库操作。

### 核心文件结构
```
src/db/
├── schema.ts       # 数据库表结构定义
├── config.ts       # Drizzle-Kit 配置
├── index.ts        # 数据库连接和查询构建器
└── migrations/     # 自动生成的迁移文件
```

## 数据库表设计

### 主要数据表
- **users** - 用户基础信息
- **orders** - 订单和支付信息  
- **tasks** - AI任务管理
- **files** - 文件存储管理
- **credits** - 积分交易记录
- **apikeys** - API密钥管理
- **affiliates** - 推广用户管理
- **feedbacks** - 用户反馈
- **posts** - 内容管理

### 表关系设计
- 用户与订单：一对多关系
- 用户与任务：一对多关系
- 任务与文件：一对多关系
- 用户与API密钥：一对多关系

## 数据操作层级

### Models层 (`src/models/`)
- **职责**：直接的数据库CRUD操作
- **原则**：每个文件对应一个主要数据实体
- **规范**：使用Drizzle类型推导，保持操作简单原子

### Services层 (`src/services/`)
- **职责**：复杂业务逻辑和跨表操作
- **规范**：处理跨模型业务逻辑，包含业务验证

## 数据库操作最佳实践

### 操作最佳实践
- **类型安全** - 使用Drizzle的 `$inferSelect` 和 `$inferInsert`
- **查询优化** - 使用索引、限制结果、选择必要字段
- **事务处理** - 原子性操作使用事务
- **错误处理** - 统一的错误处理模式
- **Edge兼容** - 考虑Cloudflare Workers的连接限制

## 数据库迁移

### 迁移管理
- **生成迁移** - `pnpm db:generate` 生成迁移文件
- **应用迁移** - `pnpm db:migrate` 应用到数据库
- **开发环境** - 可使用 `pnpm db:push` 直接推送变更
- **版本控制** - 迁移文件必须纳入版本控制

## 性能优化

### 性能优化策略
- **索引设计** - 为常用查询字段创建单列和组合索引
- **查询优化** - 限制返回结果，只选择需要的字段
- **关联查询** - 避免N+1问题，合理使用关联查询

### 3. 连接池管理
Edge环境建议使用较小的连接池配置，考虑Cloudflare Workers的连接限制。

## 数据验证

## 数据验证
- **Schema验证** - 使用Zod等库进行输入验证
- **数据库约束** - 合理使用notNull、unique等约束
- **类型安全** - 充分利用Drizzle的类型推导

## 常用命令
- `pnpm db:studio` - 启动数据库管理界面
- `pnpm db:generate` - 生成迁移文件
- `pnpm db:migrate` - 应用迁移

参考文件：
- [src/db/schema.ts](mdc:src/db/schema.ts) - 完整的表结构定义
- [src/db/config.ts](mdc:src/db/config.ts) - Drizzle配置
- [src/models/user.ts](mdc:src/models/user.ts) - 数据操作示例
description:
globs:
alwaysApply: false
---
