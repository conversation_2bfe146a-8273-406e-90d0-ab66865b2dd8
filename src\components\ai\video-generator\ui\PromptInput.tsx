"use client";

import { useTranslations } from 'next-intl';
import { useState, useEffect, useRef } from 'react';
import { Textarea } from '@/components/ui/textarea';

interface PromptInputProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  minHeight?: number;
  maxHeight?: number;
  className?: string;
  disabled?: boolean;
}

export default function PromptInput({
  value,
  onChange,
  placeholder,
  minHeight = 120,
  maxHeight = 300,
  className = '',
  disabled = false
}: PromptInputProps) {
  const t = useTranslations('components.video_generator');
  const [inputHeight, setInputHeight] = useState(minHeight);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-resize textarea based on content
  useEffect(() => {
    if (textareaRef.current) {
      // Reset height to auto to correctly calculate scroll height
      textareaRef.current.style.height = 'auto';
      
      // Calculate new height based on content (within min/max constraints)
      const scrollHeight = textareaRef.current.scrollHeight;
      const newHeight = Math.min(Math.max(scrollHeight, minHeight), maxHeight);
      
      // Set the new height
      textareaRef.current.style.height = `${newHeight}px`;
      setInputHeight(newHeight);
    }
  }, [value, minHeight, maxHeight]);

  return (
    <div className={`w-full ${className}`}>
      <Textarea
        ref={textareaRef}
        placeholder={placeholder || t('core.prompt.placeholder', { 
          fallback: 'Describe the video you want to generate...'
        })}
        value={value}
        onChange={(e) => onChange(e.target.value)}
        style={{ 
          minHeight: `${minHeight}px`,
          height: `${inputHeight}px`,
          resize: 'none'
        }}
        className="w-full p-4 text-base"
        disabled={disabled}
      />
    </div>
  );
}
