import { Models } from '@/aisdk/model-providers/replicate/image-generator';
import { 
  ModelInfo, 
} from '@/aisdk/types/common';
import { 
  AspectRatio, 
  ImageGenerationMode, 
  ImageGenerationRequest, 
  ImageStyle,
  ColorOption,
  CompositionOption,
} from '@/types/ai/image-gen-types';

/**
 * Builds parameters specifically for Replicate models
 */
export function buildReplicateParams(
  request: ImageGenerationRequest,
  model: ModelInfo,
  imageUrls?: string[]
): Record<string, any> {
  const params: Record<string, any> = {
    prompt: enhancePromptForReplicate(request),
    output_format: "png",
  };

  // Handle aspect ratio
  if (request.aspectRatio && 
      request.mode === ImageGenerationMode.TextToImage) {
    switch (request.aspectRatio) {
      case AspectRatio.Square:
        params.aspect_ratio = "1:1"
        break;
      case AspectRatio.Landscape:
        params.aspect_ratio = "16:9"
        break;
      case AspectRatio.Portrait:
        params.aspect_ratio = "9:16"
        break;
    }
  }

  const isImageToImage = request.mode === ImageGenerationMode.ImageToImage;

  switch(model.id) {
    case Models["flux-schnell"].id:
      
      break;
    case Models["flux-1.1-pro"].id:
      if (isImageToImage && imageUrls && imageUrls.length > 0) {
        params.image_prompt = imageUrls[0];
      }

      break;
    case Models["stable-diffusion-3.5-large"].id:
      if (isImageToImage && imageUrls && imageUrls.length > 0) {
        params.image = imageUrls[0];
      }

      params.prompt_strength = 0.85
      params.output_quality = 100
      break;
  }

  console.log("buildReplicateParams:", params)

  return params;
}

function enhancePromptForReplicate(request: ImageGenerationRequest): string {
  // return enhancePrompt(request);
  return request.prompt;
}


/**
 * Builds parameters specifically for KIE models
 * @param request The image generation request
 * @param webhookUrl Webhook URL for async completion notification
 * @param imageUrls Array of image URLs for image processing modes
 * @returns Parameters for KIE API
 */
export function buildKieParams(
  request: ImageGenerationRequest,
  webhookUrl: string,
  imageUrls?: string[]
): Record<string, any> {
  const params: Record<string, any> = {
    prompt: enhancePromptForKie(request),
    size: "1:1", // Default size
    isEnhance: false,
    uploadCn: false,
    callBackUrl: webhookUrl,
  };

  if(process.env.NODE_ENV === "development") {
    // 设置为 true 时使用中国大陆服务器，false 时使用海外服务器
    params.uploadCn = true;
  }

  // Handle aspect ratio
  if (request.aspectRatio) {
    switch (request.aspectRatio) {
      case AspectRatio.Square:
        params.size = "1:1";
        break;
      case AspectRatio.Landscape:
        params.size = "3:2";
        break;
      case AspectRatio.Portrait:
        params.size = "2:3";
        break;
    }
  }

  if(request.mode !== ImageGenerationMode.TextToImage 
    && imageUrls && imageUrls.length > 0) {
    params.filesUrl = imageUrls;
  }

  return params;
}

/**
 * Enhances the prompt with style and composition keywords
 * @param request Image generation request
 * @returns Enhanced prompt with additional context
 */
function enhancePromptForKie(
  request: ImageGenerationRequest
): string {
  let enhancedPrompt = request.prompt.trim();
  
  switch(request.mode) {
    case ImageGenerationMode.TextToImage:
      enhancedPrompt = `Generate a image based on the given prompt and style. ${enhancedPrompt}`;
      break;
    case ImageGenerationMode.ImageToImage:
      enhancedPrompt = `Beautify or edit the given image according to the prompt. ${enhancedPrompt}`;
      break;
    case ImageGenerationMode.ImageFusion:
      enhancedPrompt = `Merge the images into one based on the given prompt and style. ${enhancedPrompt}`;
      break;
    case ImageGenerationMode.BatchGeneration:
      break;
  }

  // Add style to prompt if specified
  if (request.imageStyle && request.imageStyle !== ImageStyle.NoStyle) {
    enhancedPrompt += `, ${request.imageStyle}`;
  }

  // Add color options if specified 
  if (request.colorOption && request.colorOption !== ColorOption.NoColor) {
    enhancedPrompt += `, ${request.colorOption}`;
  }

  // Add composition options if specified
  if (request.compositionOption && request.compositionOption !== CompositionOption.NoComposition) {
    enhancedPrompt += `, ${request.compositionOption}`;
  }

  return enhancedPrompt;
}

