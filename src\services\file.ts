/**
 * Unified File Service
 *
 * @description
 * This service provides a comprehensive set of functions to manage the entire file lifecycle
 * within the application. It acts as a central hub that orchestrates interactions between
 * the API layer, the cloud storage provider, and the database models.
 *
 * @includes
 * - Presigned URL generation for secure, direct-to-cloud client uploads.
 * - Confirmation and activation of uploaded files.
 * - Direct file creation from URLs or base64 data for server-side processes (e.g., AI generation).
 * - File retrieval, management, and metadata updates.
 * - Cleanup operations for failed or abandoned uploads.
 *
 * @design
 * The service is designed to be the single source of truth for file-related business logic,
 * ensuring consistency and decoupling the API controllers from the low-level implementation details
 * of storage and database access.
 */
import { v4 as uuidv4 } from "uuid";
import { 
  createPresignedUploadUrl,
  createPresignedDownloadUrl,
  uploadFromUrl,
  uploadFromBuffer,
  verifyFileExists,
  deleteFiles,
} from "@/lib/cloud-storage";
import { getExtensionFromMimeType, extractFileInfoFromUrl } from "@/lib/mime";
import logger from "@/lib/logger";
import { parseBase64 } from "@/lib/file";
import {
  FileRecord,
  FileCreationData,
  FileCreationResult,
  FileStatus,
  FileMetadata,
  FileDataEntry,
  FileCategory,
  TaskFileQueryCategory,
  AccessLevel,
  FileInfo,
} from "@/types/file";
import {
  FileUploadInfo,
  PresignedUploadInfo,
} from "@/types/file-upload";
import {
  createFile,
  getFileById,
  softDeleteFile as softDeleteFileInDb,
  updateFileStatus,
  updateFileTaskId as updateFileTaskIdInDb,
  getUserStorageStats as getUserStorageStatsFromDb,
  createPendingFile,
  getFileByIdAndStatus,
  updateFileAccessLevel,
  getActiveTaskFiles,
  getActiveUserFilesPaginated,
  getActiveUserFilesByUuid,
  getPendingFilesOlderThan,
  hardDeleteFileById,
  getMinimalFileRecords,
  hardDeleteFilesByIds,
} from "@/models/file";
import { getFormattedDateTime } from "@/lib/time";

// ==================== Helper Functions ====================

/** Generates a new unique file ID. */
export function generateFileId(): string {
  // return `file_${uuidv4()}`;
  return uuidv4();
}

/** Generates a filename for a newly created file. */
function generateFilename(mimeType: string): string {
  const extension = getExtensionFromMimeType(mimeType);

  const uuid = uuidv4().substring(0, 8);
  const dateTime = getFormattedDateTime();
  let filename = `${dateTime}_${uuid}.${extension}`;

  // Final sanitization to ensure the entire filename is safe
  // filename = sanitizeFilePath(filename, { toLower: true });

  return filename;
}

/**
 * Generates the cloud storage key (path) for a file based on its category.
 * @param category The business category of the file.
 * @param context Additional context like user or task IDs.
 * @returns The generated storage key.
 */
export function generateStorageKey(
  category: FileCategory,
  mimeType: string,
  context: { 
    userUuid?: string, 
    taskId?: string, 
    postId?: string 
  }
): string {
  const now = new Date();
  const year = now.getFullYear();
  const month = (now.getMonth() + 1).toString().padStart(2, '0');
  // const day = now.getDate().toString().padStart(2, '0');
  const { userUuid, taskId, postId } = context;

  const filename = generateFilename(mimeType);
  let path = "";
  switch (category) {
    case FileCategory.USER_PROFILE:
      path = `${FileCategory.USER_PROFILE}/${userUuid}`;
      break;
    case FileCategory.USER_UPLOADS:
      path = `${FileCategory.USER_UPLOADS}/${year}/${month}/${userUuid}`;
      break;
    case FileCategory.TASK_OUTPUT:
      path = `${FileCategory.TASK_OUTPUT}/${year}/${month}/${taskId}`;
      break;
    case FileCategory.BLOGS:
      path = `${FileCategory.BLOGS}/${year}/${month}/${postId}`;
      break;
    case FileCategory.TEMP:
      path = `${FileCategory.TEMP}/${year}/${month}`;
      break;
    default:
      // Fallback for safety, though should ideally not be reached
      const un: never = category;
      logger.warn(`Unhandled file category in generateStorageKey: ${un}`);
      path = `${FileCategory.TEMP}/${year}/${month}`;
      break;
  }

  return `${path}/${filename}`;
}

/**
 * Determines the access level for a file based on its category.
 * @param category The file's category.
 * @returns The appropriate access level.
 */
export function determineAccessLevel(category: FileCategory): AccessLevel {
  switch (category) {
    case FileCategory.USER_PROFILE:
    case FileCategory.BLOGS:
      return AccessLevel.PUBLIC;
    
    case FileCategory.USER_UPLOADS:
    case FileCategory.TASK_OUTPUT:
    case FileCategory.TEMP:
      return AccessLevel.PRIVATE;
      
    default:
      // Default to private for security
      const un: never = category;
      logger.warn(`Unhandled file category in determineAccessLevel: ${un}`);
      return AccessLevel.PRIVATE;
  }
}

// ==================== Presigned Upload Flow ====================

/**
 * Creates a batch of presigned URLs for client-side uploads.
 * Client is responsible for all file validation and processing.
 */
export async function createPresignedUploads(
  filesInfo: FileUploadInfo[],
  fileCategory: FileCategory,
  userUuid: string
): Promise<PresignedUploadInfo[]> {
    const presignedUploadInfos: PresignedUploadInfo[] = [];

    for (const fileInfo of filesInfo) {
      try {
        const fileId = generateFileId();
        const storageKey = generateStorageKey(
          fileCategory, fileInfo.mimeType, { userUuid });
        const accessLevel = determineAccessLevel(fileCategory);
        
        // Trust client-side validation completely - no server-side file size limits
        const presignedInfo = await createPresignedUploadUrl({
          storageKey,
          mimeType: fileInfo.mimeType,
        });

        logger.info(
          `Presigned upload URL created`, 
          presignedInfo, 
          {
            filePath: "services/file.ts",
            functionName: 'createPresignedUploads'
          });

        await createPendingFile({
          file_id: fileId,
          user_uuid: userUuid,
          storage_key: presignedInfo.storageKey,
          file_name: fileInfo.fileName,
          mime_type: fileInfo.mimeType,
          file_size: fileInfo.fileSize,
          file_category: fileCategory,
          access_level: accessLevel,
          metadata: {
            upload_method: 'presigned',
            original_filename: fileInfo.fileName,
            client_validated: true, // Mark as client-side validated
          },
        });

        presignedUploadInfos.push({
          fileId,
          presignedUrl: presignedInfo.presignedUrl,
          fields: presignedInfo.fields,
          expiresAt: presignedInfo.expiresAt,
        });
      } catch (error) {
        logger.error('Failed to create presigned upload for individual file', error as Error, {
          fileName: fileInfo.fileName,
        });
        // Continue to next file
      }
    }

    return presignedUploadInfos;
}

/**
 * Confirms that a batch of files has been successfully uploaded by the client.
 * Minimal server-side validation - trust client processing.
 */
export async function confirmBatchUpload(fileIds: string[], userUuid: string): Promise<FileInfo[]> {
    const confirmedFiles: FileInfo[] = [];

    for (const fileId of fileIds) {
      try {
        const fileRecord = await getFileByIdAndStatus(fileId, FileStatus.PENDING);
        
        // Basic security checks only
        if (!fileRecord || fileRecord.user_uuid !== userUuid) {
          logger.warn('File record not found, not pending, or owner mismatch', { fileId, userUuid });
          continue;
        }

        // Only verify file existence - trust client for size and type validation
        const { exists } = await verifyFileExists(fileRecord.storage_key);
        if (!exists) {
          logger.warn('File not found in cloud storage upon confirmation', { fileId });
          continue;
        }
        
        const activatedRecord = await updateFileStatus(fileId, FileStatus.ACTIVE);
        if (!activatedRecord) {
          logger.error('Failed to activate file record', { fileId });
          continue;
        }

        confirmedFiles.push({
          fileId: activatedRecord.file_id,
          fileName: activatedRecord.file_name,
          fileSize: activatedRecord.file_size,
          mimeType: activatedRecord.mime_type,
          category: activatedRecord.file_category,
          url: generateFileUrl(activatedRecord),
        });

      } catch (error) {
        logger.error('Failed to confirm individual file upload', error as Error, { fileId });
      }
    }

    return confirmedFiles;
}

// ==================== Server-Side File Creation ====================

/**
 * Creates files from a list of public URLs (e.g., from an AI provider).
 */
export async function createFilesFromUrls(
  urls: string[],
  userUuid: string,
  category: FileCategory,
  taskId?: string, 
  context?: { 
    metadata: Partial<FileMetadata> 
  }
): Promise<FileCreationResult[]> {
  const creationResults: FileCreationResult[] = [];

  for (const url of urls) {
    try {
      // Extract content type from URL or use default
      const { mimeType } = extractFileInfoFromUrl(url);
      const contentType = mimeType;
      
      const fileId = generateFileId();
      const storageKey = generateStorageKey(
        category, contentType, { userUuid, taskId });
      
      // uploadFromUrl already handles fetching with retries, timeout, and error handling
      // Remove the duplicate fetch call that was causing the issue
      const uploadResult = await uploadFromUrl(url, storageKey, contentType);

      if (!uploadResult.key) {
        logger.error('Upload from URL did not return a storage key', { url, storageKey });
        continue;
      }

      const fileRecord = await createCompleteFileRecord({
        file_id: fileId,
        user_uuid: userUuid,
        task_id: taskId,
        storage_key: uploadResult.key,
        file_name: uploadResult.filename || "",
        mime_type: contentType,
        file_size: uploadResult.fileSize || 0, // Use actual file size from upload result
        file_category: category,
        access_level: determineAccessLevel(category),
        metadata: {
          original_url: url,
          upload_method: 'url_download',
          ...(context?.metadata || {}),
        }
      });
      
      creationResults.push({
        file_record: fileRecord,
        url: generateFileUrl(fileRecord),
      });

    } catch (error) {
      logger.error('Failed to create file from URL', error, { url });
      // Continue to next URL instead of stopping the entire process
    }
  }

  return creationResults;
}

/**
 * Creates files from base64-encoded strings.
 */
export async function createFilesFromBase64(
  filesData: FileDataEntry[],
  userUuid: string,
  category: FileCategory,
  context: { taskId?: string, source?: string, metadata?: Partial<FileMetadata> }
): Promise<FileCreationResult[]> {
  const creationResults: FileCreationResult[] = [];

  for (const fileData of filesData) {
    try {
      const { pureBase64 } = parseBase64(fileData.base64Data);
      const buffer = Buffer.from(pureBase64, 'base64');
      
      const fileId = generateFileId();
      const storageKey = generateStorageKey(
        category, fileData.mimeType, { userUuid, taskId: context.taskId });

      const uploadResult = await uploadFromBuffer(buffer, storageKey, fileData.mimeType);
          
      const fileRecord = await createCompleteFileRecord({
        file_id: fileId,
        user_uuid: userUuid,
        task_id: context.taskId,
        storage_key: uploadResult.key || "",
        file_name: uploadResult.filename || "",
        mime_type: fileData.mimeType,
        file_size: buffer.length,
        file_category: category,
        access_level: determineAccessLevel(category),
        metadata: {
          source: context.source,
          upload_method: 'base64',
          ...context.metadata,
        }
      });
          
      creationResults.push({
            file_record: fileRecord,
        url: generateFileUrl(fileRecord),
      });

        } catch (error) {
      logger.error('Failed to create file from base64', error, { mimeType: fileData.mimeType });
    }
  }

  return creationResults;
}


// ==================== File Retrieval and URL Generation ====================

/** Constructs the public-facing URL for a file record. */
export function generateFileUrl(fileRecord: FileRecord): string {
  const storageDomain = process.env.STORAGE_DOMAIN;
  if (!storageDomain) {
    logger.warn('STORAGE_DOMAIN environment variable is not set. URLs may be incorrect.');
    return '';
  }
  
  const cleanDomain = storageDomain.endsWith('/') ? storageDomain.slice(0, -1) : storageDomain;
  return `${cleanDomain}/${fileRecord.storage_key}`;
}

/**
 * Retrieves an active file record.
 * @param fileId The unique identifier for the file.
 * @returns The `FileRecord` or null if not found.
 */
export async function getFileRecord(fileId: string): Promise<FileRecord | null> {
  return getFileById(fileId);
}

/**
 * Generates a presigned URL that forces a browser download.
 * @param fileId The ID of the file to download.
 * @returns A presigned URL string, or null if access is denied or file not found.
 */
export async function getPresignedDownloadUrl(fileId: string): Promise<string | null> {
  const file = await getFileById(fileId);
  if (!file) {
    logger.error('File not found', { fileId }, {
      filePath: "services/file.ts",
      functionName: 'getPresignedDownloadUrl'
    });
    return null;
  }
  return createPresignedDownloadUrl({
    storageKey: file.storage_key,
    customFilename: file.file_name,
  });
}

/**
 * Gets the access URL for a file.
 * @param fileId The ID of the file to get URL for.
 * @returns The file access URL string, or null if file not found.
 */
export async function getFileAccessUrl(fileId: string): Promise<string | null> {
  const file = await getFileById(fileId);
  if (!file) {
    logger.error('File not found', { fileId }, {
      filePath: "services/file.ts",
      functionName: 'getFileAccessUrl'
    });
    return null;
  }
  return generateFileUrl(file);
}

/**
 * Retrieves files associated with a task, optionally filtered by category.
 * @param taskId The task ID.
 * @param category Category filter. Only accepts 'all', FileCategory.USER_UPLOADS, or FileCategory.TASK_OUTPUT.
 * @returns Array of file records.
 */
export async function getTaskFiles(
  taskId: string, 
  category: TaskFileQueryCategory = 'all'
): Promise<FileRecord[]> {
  return getActiveTaskFiles(taskId, category);
}

/** Retrieves a paginated list of files for a specific user. */
export async function getUserFiles(
  userUuid: string,
  category?: FileCategory,
  limit: number = 20,
  offset: number = 0
): Promise<FileRecord[]> {
  return getActiveUserFilesPaginated(userUuid, category, limit, offset);
}

/** Retrieves storage usage statistics for a user. */
export async function getUserStorageStats(userUuid: string) {
  return getUserStorageStatsFromDb(userUuid);
}


// ==================== File Management & Cleanup ====================

/** Associates an existing file with an AI task ID. */
export async function updateFileByTaskId(fileId: string, taskId: string): Promise<FileRecord> {
  return updateFileTaskIdInDb(fileId, taskId);
}

/** Changes the access level of a file. */
export async function changeFileAccessLevel(fileId: string, userUuid: string, newLevel: AccessLevel): Promise<FileRecord> {
  const file = await getFileById(fileId);
  if (!file || file.user_uuid !== userUuid) {
    throw new Error(`File not found or access denied: ${fileId}`);
  }
  
  return updateFileAccessLevel(fileId, newLevel);
}

/**
 * Deletes files from storage and soft-deletes the database records.
 * @param fileIds Array of file IDs to delete.
 * @param userUuid The user requesting the deletion (for ownership verification).
 */
export async function deleteUserFiles(fileIds: string[], userUuid: string): Promise<void> {
  const allUserFiles = await getActiveUserFilesByUuid(userUuid);
  const recordsToDelete = allUserFiles.filter(f => fileIds.includes(f.file_id));

  if (recordsToDelete.length === 0) return;

  const storageKeysToDelete = recordsToDelete.map(r => r.storage_key);
  await deleteFiles(storageKeysToDelete);
  
  for (const record of recordsToDelete) {
    await softDeleteFileInDb(record.file_id);
  }
  logger.info(`Deleted ${recordsToDelete.length} files for user ${userUuid}`);
}

// ==================== File Cleanup ====================

/**
 * Cleans up failed or abandoned 'pending' uploads.
 * This should be run by a scheduled job.
 */
export async function cleanupPendingUploads(): Promise<void> {
  const twoHoursAgo = new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString();
  const pendingFiles = await getPendingFilesOlderThan(twoHoursAgo);
  
  if (pendingFiles.length === 0) return;

  logger.info(`Starting cleanup of ${pendingFiles.length} abandoned pending uploads.`);

  const storageKeysToDelete = pendingFiles.map(f => f.storage_key);
  await deleteFiles(storageKeysToDelete);

  for (const file of pendingFiles) {
    // This is a hard delete from the database for pending records
    const success = await hardDeleteFileById(file.id);
    if (!success) {
      logger.error('Failed to hard-delete pending file record during cleanup', { fileId: file.file_id });
    }
  }
}

/**
 * Cleans up failed upload files by removing them from storage and database.
 * This function is typically called when client-side uploads fail or are abandoned.
 * 
 * @param fileIds Array of file IDs to clean up
 * @returns Promise<boolean[]> Array indicating success/failure for each file ID
 */
export async function cleanupFailedUploads(
  fileIds: string[]
): Promise<boolean[]> {
  if (!fileIds || !Array.isArray(fileIds) || fileIds.length === 0) {
    logger.warn('No file IDs provided to cleanupFailedUploads');
    return [];
  }

  logger.info(`Starting cleanup of ${fileIds.length} failed upload files`);
  const results: boolean[] = [];
  const filesToDelete: FileRecord[] = [];
  const storageKeysToDelete: string[] = [];

  // Step 1: Collect all valid file records that need cleanup
  for (let i = 0; i < fileIds.length; i++) {
    const fileId = fileIds[i];
    
    try {
      const file = await getFileById(fileId);
      
      if (!file) {
        // File doesn't exist in database - consider this successful cleanup
        logger.info(`File record not found, considering cleanup successful: ${fileId}`);
        results[i] = true;
        continue;
      }

      // Only cleanup files that are in PENDING, FAILED, or DELETED status
      if (file.status !== FileStatus.PENDING && 
          file.status !== FileStatus.FAILED && 
          file.status !== FileStatus.DELETED) 
      {
        logger.warn(`Attempted to cleanup file with status ${file.status}: ${fileId}`);
        results[i] = false;
        continue;
      }

      filesToDelete.push(file);
      storageKeysToDelete.push(file.storage_key);
      
      // Mark as will be processed (will be updated based on actual cleanup result)
      results[i] = false;
      
    } catch (error) {
      logger.error('Error retrieving file record during cleanup', error as Error, { fileId });
      results[i] = false;
    }
  }

  if (filesToDelete.length === 0) {
    logger.info('No files require cleanup');
    return results;
  }

  // Step 2: Delete files from cloud storage (batch operation)
  try {
    await deleteFiles(storageKeysToDelete);
    logger.info(`Successfully deleted ${storageKeysToDelete.length} files from cloud storage`);
  } catch (error) {
    logger.error('Failed to delete files from cloud storage during cleanup', error as Error, {
      fileCount: storageKeysToDelete.length
    });
    // Continue with database cleanup even if storage deletion fails
  }

  // Step 3: Hard delete file records from database
  const fileIdsToDelete = filesToDelete.map(f => f.file_id);
  const recordIdsToDelete = filesToDelete.map(f => f.id);

  try {
    const success = await hardDeleteFilesByIds(recordIdsToDelete);
    
    if (success) {
      // Mark all processed files as successfully cleaned up
      for (let i = 0; i < fileIds.length; i++) {
        if (fileIdsToDelete.includes(fileIds[i])) {
          results[i] = true;
        }
      }
      
      logger.info(`Successfully cleaned up ${filesToDelete.length} failed upload files`);
    } else {
      logger.error('Failed to delete file records from database during cleanup');
      // Results remain false for processed files
    }
    
  } catch (error) {
    logger.error('Error during database cleanup of failed uploads', error as Error, {
      fileCount: filesToDelete.length
    });
    // Results remain false for processed files
  }

  // Log summary
  const successCount = results.filter(r => r).length;
  const failureCount = results.length - successCount;
  
  if (failureCount > 0) {
    logger.warn(`Cleanup completed with ${successCount} successes and ${failureCount} failures`);
  } else {
    logger.info(`Successfully cleaned up all ${successCount} requested files`);
  }

  return results;
}

// ==================== File Creation ====================

/** Creates a complete and 'active' file record in the database in one step. */
async function createCompleteFileRecord(fileData: FileCreationData): Promise<FileRecord> {
  return createFile(fileData);
}

/**
 * Hard-deletes file records and their corresponding storage objects.
 * USE WITH CAUTION. This is for permanent data removal.
 *
 * @description
 * This function orchestrates a permanent deletion. It accepts business-facing `file_id`s,
 * translates them into internal database primary keys (`id`) for performance,
 * deletes the objects from cloud storage, and then permanently removes the records
 * from the database.
 *
 * @param fileIds An array of file IDs to permanently delete.
 */
export async function hardDeleteFiles(fileIds: string[]): Promise<void> {
  logger.warn(`Initiating hard delete for ${fileIds.length} files. This is irreversible.`);
  
  const records = await getMinimalFileRecords(fileIds);

  if (!records || records.length === 0) {
    logger.info('No matching records found for hard deletion.');
    return;
  }

  const storageKeysToDelete = records.map(r => r.storage_key);
  await deleteFiles(storageKeysToDelete);
  
  const recordIdsToDelete = records.map(r => r.id);
  const success = await hardDeleteFilesByIds(recordIdsToDelete);

  if (success) {
    logger.info(`Successfully hard-deleted ${records.length} files and their records.`);
  } else {
    logger.error('Hard deletion of file records from database failed.', { count: recordIdsToDelete.length });
  }
}
