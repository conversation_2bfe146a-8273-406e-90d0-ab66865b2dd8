# FileUploader 组件设计文档

## 🎯 设计理念

### 核心原则
- **通用性优先** - 设计为支持多种文件类型的通用上传组件
- **渐进增强** - 从基础功能开始，逐步添加高级特性
- **响应式设计** - 适配不同屏幕尺寸和使用场景
- **用户体验** - 提供直观的交互和实时反馈

### 架构分层
```
┌─────────────────────────────────────────────────────────────┐
│                    UI Layer (展示层)                        │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐ │
│  │  ImageUploader  │ │  VideoUploader  │ │ DocumentUploader│ │
│  │  ┌───────────┐  │ │  ┌───────────┐  │ │  ┌───────────┐  │ │
│  │  │ImagePreview│  │ │  │VideoPreview│  │ │  │DocPreview │  │ │
│  │  │Compressor │  │ │  │Compressor │  │ │  │Viewer     │  │ │
│  │  └───────────┘  │ │  └───────────┘  │ │  └───────────┘  │ │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘ │
├─────────────────────────────────────────────────────────────┤
│                FileUploader (通用核心)                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐             │
│  │ UploadArea  │ │FilePreview  │ │ProgressBar │             │
│  │ (拖拽区域)   │ │ (预览工厂)   │ │ (进度显示)   │             │
│  └─────────────┘ └─────────────┘ └─────────────┘             │
├─────────────────────────────────────────────────────────────┤
│                  Processor Layer (处理层)                    │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐             │
│  │ImageProcessor│ │VideoProcessor│ │FileValidator│             │
│  │(压缩/转换)    │ │(压缩/转码)    │ │(类型/大小)   │             │
│  └─────────────┘ └─────────────┘ └─────────────┘             │
├─────────────────────────────────────────────────────────────┤
│                 Interface Layer (接口层)                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐             │
│  │UploadAdapter│ │ProgressAdapter│ │ErrorAdapter │             │
│  │(上传抽象)    │ │ (进度抽象)    │ │ (错误抽象)   │             │
│  └─────────────┘ └─────────────┘ └─────────────┘             │
├─────────────────────────────────────────────────────────────┤
│                 Service Layer (服务层)                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐             │
│  │HTTP Service │ │Storage API  │ │Config Mgr   │             │
│  │(请求封装)    │ │ (云存储)     │ │ (配置管理)   │             │
│  └─────────────┘ └─────────────┘ └─────────────┘             │
└─────────────────────────────────────────────────────────────┘
```

## 🚀 功能特性

### 当前功能
- ✅ **多文件上传** - 支持批量文件选择和上传
- ✅ **拖拽上传** - 直观的拖拽交互体验
- ✅ **预签名上传** - 安全的直传云存储方案
- ✅ **实时进度** - 上传进度实时反馈
- ✅ **文件验证** - 类型、大小等验证机制
- ✅ **响应式布局** - 适配移动端和桌面端
- ✅ **主题支持** - 深色/浅色主题切换
- ✅ **智能清理** - 文件删除时同步清理数据库和存储桶
- ✅ **动态布局** - 根据文件数量智能调整容器尺寸

### 扩展特性 (规划中)
- 🔄 **断点续传** - 网络中断后自动恢复上传
- 📱 **移动端优化** - 相机拍照、相册选择
- 🎨 **自定义样式** - 更灵活的主题定制
- 📊 **统计分析** - 上传成功率、速度统计
- 🔒 **权限控制** - 基于用户角色的上传限制
- 🎯 **智能压缩** - 自动压缩大文件

## 📐 组件设计模式

### 1. Variant 模式
```typescript
type FileUploaderVariant = 'default' | 'compact' | 'large';

interface FileUploaderProps {
  variant?: FileUploaderVariant;
  // 不同变体适用于不同场景：
  // - default: 标准上传区域，显示进度和状态
  // - compact: 紧凑模式，用于网格布局中
  // - large: 大尺寸模式，隐藏状态信息避免遮挡
}
```

### 2. 动态布局系统
```typescript
interface LayoutConfig {
  mode: 'single' | 'grid';           // 布局模式
  gridClass: string;                 // CSS Grid 类名
  maxWidth: number;                  // 容器最大宽度
  itemSize: number;                  // 单个项目尺寸
  containerWidth: number;            // 实际容器宽度
}

// 布局计算逻辑：
// 1. 无文件时使用单一布局，提供最佳初始体验
// 2. 有文件时根据数量动态切换网格布局
// 3. 2×2 网格：保持原始容器尺寸
// 4. 2×3 网格：向右扩展容器宽度
```

### 3. 回调通信模式
```typescript
interface FileUploaderProps {
  // 标准回调
  onUploadComplete: (results: UploadResult[], files: File[]) => void;
  onUploadError?: (error: string) => void;
  
  // 布局通信回调
  onContainerWidthChange?: (width: number) => void;
  
  // 状态回调
  onFileRemove?: (index: number) => void;
  onProgressChange?: (progress: number) => void;
}
```

### 4. 文件清理机制
```typescript
// 自定义Hook：统一文件清理接口
export function useFileCleanup(userUuid?: string) {
  const cleanupFiles = useCallback(async (
    fileIds: string[], 
    reason?: string
  ): Promise<boolean> => {
    // 调用后端API同步删除数据库和存储桶中的文件
    // 支持批量清理，提高效率
    // 包含详细的错误处理和日志记录
  }, [userUuid]);

  return { cleanupFiles };
}

// 清理策略：
// 1. 延迟清理 - 用户删除文件时记录意图，不立即清理
// 2. 任务提交清理 - 在生成任务前清理被删除的文件
// 3. 表单清空清理 - Clear按钮清理所有相关文件
```

## 🔧 技术实现

### 智能布局系统

#### 动态容器宽度扩展
```typescript
// 布局配置计算器
function getLayoutConfig(fileCount: number, maxImages: number): LayoutConfig {
  if (fileCount === 0) {
    return {
      mode: 'single',
      gridClass: '',
      maxWidth: 216,
      itemSize: 216,
      containerWidth: 216  // 关键：动态容器宽度
    };
  }
  
  // 2×2 布局：2-3张图片
  if (fileCount <= 3 || maxImages <= 3) {
    return {
      mode: 'grid',
      gridClass: 'grid-cols-2',
      maxWidth: 216,
      itemSize: 100,
      containerWidth: 216  // 保持原始宽度
    };
  }
  
  // 2×3 布局：4-5张图片  
  return {
    mode: 'grid',
    gridClass: 'grid-cols-3',
    maxWidth: 328,
    itemSize: 100,
    containerWidth: 328  // 向右扩展宽度
  };
}

// 父子组件通信：动态布局反馈
const ImageUploader = ({ onContainerWidthChange }) => {
  const currentConfig = getLayoutConfig(uploadedFiles.length, maxImages);
  
  useEffect(() => {
    // 通知父组件宽度变化，父组件调整整体布局
    onContainerWidthChange?.(currentConfig.containerWidth);
  }, [currentConfig.containerWidth, onContainerWidthChange]);
};
```

### 文件清理系统

#### 清理策略设计
```typescript
// 用户操作 -> 清理行为映射
interface CleanupStrategy {
  // 删除单个文件：记录删除意图（允许后悔）
  onFileRemove: (fileId: string) => void;
  
  // 任务提交：清理已删除文件（确认不再需要）
  onTaskSubmit: (removedFileIds: string[]) => Promise<void>;
  
  // 表单清空：清理所有文件（重新开始）
  onFormClear: (allFileIds: string[]) => Promise<void>;
}

// 实际应用示例
const ImageGenerator = () => {
  const [removedFileIds, setRemovedFileIds] = useState<string[]>([]);
  const { cleanupFiles } = useFileCleanup(user?.uuid);
  
  const handleFileRemove = (index: number) => {
    const removedFile = uploadedFiles[index];
    
    // 立即从UI移除
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
    
    // 记录删除意图（延迟清理）
    if (removedFile?.fileId) {
      setRemovedFileIds(prev => [...prev, removedFile.fileId]);
    }
  };
  
  const handleGenerate = async () => {
    // 任务提交前清理已删除的文件
    if (removedFileIds.length > 0) {
      await cleanupFiles(removedFileIds, 'removed by user');
      setRemovedFileIds([]);
    }
    // ... 继续任务逻辑
  };
  
  const handleClear = async () => {
    // 收集所有需要清理的文件（显示的 + 已删除的）
    const allFileIds = [
      ...uploadedFiles.map(f => f.fileId).filter(Boolean),
      ...removedFileIds
    ];
    
    // 表单清空时清理所有文件
    if (allFileIds.length > 0) {
      await cleanupFiles(allFileIds, 'form cleared');
    }
    
    // 重置状态
    setUploadedFiles([]);
    setRemovedFileIds([]);
  };
};
```

### 文件类型系统设计

#### 文件类型定义和分类
```typescript
// types/file-categories.ts
export const FILE_CATEGORIES = {
  IMAGE: {
    types: ['image/jpeg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'],
    extensions: ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.svg'],
    maxSize: 10 * 1024 * 1024, // 10MB
    features: ['compression', 'preview', 'editing']
  },
  VIDEO: {
    types: ['video/mp4', 'video/webm', 'video/quicktime', 'video/avi'],
    extensions: ['.mp4', '.webm', '.mov', '.avi'],
    maxSize: 500 * 1024 * 1024, // 500MB
    features: ['compression', 'preview', 'thumbnail']
  },
  DOCUMENT: {
    types: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ],
    extensions: ['.pdf', '.doc', '.docx', '.txt'],
    maxSize: 50 * 1024 * 1024, // 50MB
    features: ['preview', 'text-extraction']
  },
  AUDIO: {
    types: ['audio/mpeg', 'audio/wav', 'audio/ogg', 'audio/mp4'],
    extensions: ['.mp3', '.wav', '.ogg', '.m4a'],
    maxSize: 20 * 1024 * 1024, // 20MB
    features: ['compression', 'waveform']
  },
  ARCHIVE: {
    types: ['application/zip', 'application/x-rar-compressed', 'application/x-7z-compressed'],
    extensions: ['.zip', '.rar', '.7z'],
    maxSize: 100 * 1024 * 1024, // 100MB
    features: ['extraction', 'listing']
  }
} as const;

// 文件类型检测器
export class FileTypeDetector {
  static detectCategory(file: File): keyof typeof FILE_CATEGORIES | null {
    for (const [category, config] of Object.entries(FILE_CATEGORIES)) {
      if (config.types.includes(file.type)) {
        return category as keyof typeof FILE_CATEGORIES;
      }
    }
    return null;
  }
  
  static validateFile(file: File): ValidationResult {
    const category = this.detectCategory(file);
    if (!category) {
      return { valid: false, error: 'Unsupported file type' };
    }
    
    const config = FILE_CATEGORIES[category];
    if (file.size > config.maxSize) {
      return { valid: false, error: `File too large. Max size: ${config.maxSize / 1024 / 1024}MB` };
    }
    
    return { valid: true, category, features: config.features };
  }
}
```

#### 组件文件结构规划
```
components/ui/file-uploader/
├── FileUploader.tsx              # 通用核心组件
├── FileUploader.md               # 设计文档
├── index.ts                      # 导出入口
hooks/
├── useFileCleanup.ts             # 文件清理Hook
├── types/
│   ├── file-categories.ts        # 文件类型定义
│   ├── upload-interfaces.ts      # 上传接口定义
│   └── processor-interfaces.ts   # 处理器接口
├── adapters/                     # 接口适配器层（解耦关键）
│   ├── UploadAdapter.ts         # 上传服务抽象
│   ├── ProgressAdapter.ts       # 进度回调抽象
│   └── ErrorAdapter.ts          # 错误处理抽象
├── processors/                   # 文件处理器
│   ├── ImageProcessor.ts        # 图片压缩/转换
│   ├── VideoProcessor.ts        # 视频压缩/转码
│   └── FileValidator.ts         # 通用文件验证
├── previews/                     # 预览组件
│   ├── PreviewFactory.ts        # 预览工厂
│   ├── ImagePreview.tsx         # 图片预览
│   ├── VideoPreview.tsx         # 视频预览
│   ├── DocumentPreview.tsx      # 文档预览
│   ├── AudioPreview.tsx         # 音频预览
│   └── GenericPreview.tsx       # 通用文件预览
├── specialized/                  # 专用上传器
│   ├── ImageUploader.tsx        # 图片专用（当前已实现）
│   ├── VideoUploader.tsx        # 视频专用
│   ├── DocumentUploader.tsx     # 文档专用
│   └── MultiTypeUploader.tsx    # 多类型混合
├── ui-components/               # UI基础组件
│   ├── UploadArea.tsx          # 拖拽上传区域
│   ├── ProgressBar.tsx         # 进度条
│   ├── FileList.tsx            # 文件列表
│   └── CompressOptions.tsx     # 压缩选项面板
└── utils/                       # 工具函数
    ├── file-utils.ts           # 文件处理工具
    ├── compression-utils.ts    # 压缩工具
    └── validation-utils.ts     # 验证工具
```

### 接口设计与解耦策略

#### 上传适配器接口 (解耦核心)
```typescript
// adapters/UploadAdapter.ts
export interface UploadAdapter {
  // 核心上传方法
  upload(file: File, options: UploadOptions): Promise<UploadResult>;
  
  // 批量上传
  uploadBatch(files: File[], options: BatchUploadOptions): Promise<UploadResult[]>;
  
  // 上传进度回调
  onProgress?(callback: (progress: ProgressInfo) => void): void;
  
  // 取消上传
  abort(uploadId: string): void;
  
  // 获取上传配置
  getConfig(): UploadConfig;
}

// 具体实现可以是：
// - PresignedUploadAdapter (预签名直传)
// - ServerProxyUploadAdapter (服务器中转) 
// - HybridUploadAdapter (混合策略)
// - MockUploadAdapter (测试用)

// 使用工厂模式创建适配器
export class UploadAdapterFactory {
  static create(strategy: 'presigned' | 'server' | 'hybrid'): UploadAdapter {
    switch (strategy) {
      case 'presigned':
        return new PresignedUploadAdapter();
      case 'server':
        return new ServerProxyUploadAdapter();
      case 'hybrid':
        return new HybridUploadAdapter();
      default:
        throw new Error(`Unsupported upload strategy: ${strategy}`);
    }
  }
}
```

#### 文件处理器接口
```typescript
// processors/interfaces.ts
export interface FileProcessor<T = any> {
  // 是否支持该文件类型
  supports(file: File): boolean;
  
  // 处理文件（压缩、转换等）
  process(file: File, options: ProcessOptions): Promise<ProcessResult<T>>;
  
  // 获取处理器信息
  getInfo(): ProcessorInfo;
}

// 图片处理器
export interface ImageProcessor extends FileProcessor<ImageProcessResult> {
  compress(file: File, quality: number): Promise<File>;
  resize(file: File, dimensions: Dimensions): Promise<File>;
  convert(file: File, format: ImageFormat): Promise<File>;
}

// 视频处理器
export interface VideoProcessor extends FileProcessor<VideoProcessResult> {
  compress(file: File, options: VideoCompressOptions): Promise<File>;
  extractThumbnail(file: File, time?: number): Promise<File>;
  getMetadata(file: File): Promise<VideoMetadata>;
}
```

#### 预览组件接口
```typescript
// previews/interfaces.ts
export interface FilePreviewComponent {
  // 组件支持的文件类型
  supportedTypes: string[];
  
  // 渲染预览
  render(file: FileInfo, options?: PreviewOptions): React.ReactNode;
  
  // 获取预览缩略图
  getThumbnail?(file: FileInfo): Promise<string>;
}

// 预览工厂
export class PreviewFactory {
  private static previews: Map<string, FilePreviewComponent> = new Map();
  
  static register(types: string[], component: FilePreviewComponent) {
    types.forEach(type => {
      this.previews.set(type, component);
    });
  }
  
  static createPreview(file: FileInfo): React.ReactNode {
    const component = this.previews.get(file.mimeType);
    return component ? component.render(file) : <GenericPreview file={file} />;
  }
}
```

### 客户端压缩处理

#### 图片压缩策略
```typescript
// processors/ImageProcessor.ts
export class ClientImageProcessor implements ImageProcessor {
  async compress(file: File, options: ImageCompressOptions): Promise<File> {
    const { quality = 0.8, maxWidth = 1920, maxHeight = 1080 } = options;
    
    return new Promise((resolve) => {
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d')!;
      const img = new Image();
      
      img.onload = () => {
        // 计算压缩后尺寸
        const { width, height } = this.calculateDimensions(img, maxWidth, maxHeight);
        
        canvas.width = width;
        canvas.height = height;
        
        // 绘制并压缩
        ctx.drawImage(img, 0, 0, width, height);
        
        canvas.toBlob((blob) => {
          if (blob) {
            const compressedFile = new File([blob], file.name, {
              type: file.type,
              lastModified: Date.now()
            });
            resolve(compressedFile);
          }
        }, file.type, quality);
      };
      
      img.src = URL.createObjectURL(file);
    });
  }
  
  private calculateDimensions(img: HTMLImageElement, maxWidth: number, maxHeight: number) {
    let { width, height } = img;
    
    if (width > maxWidth || height > maxHeight) {
      const ratio = Math.min(maxWidth / width, maxHeight / height);
      width *= ratio;
      height *= ratio;
    }
    
    return { width: Math.round(width), height: Math.round(height) };
  }
}
```

#### 视频压缩策略 (客户端性能考量)
```typescript
// processors/VideoProcessor.ts
export class ClientVideoProcessor implements VideoProcessor {
  async compress(file: File, options: VideoCompressOptions): Promise<File> {
    // 客户端视频压缩需要考虑性能限制
    const { maxSize = 50 * 1024 * 1024, quality = 'medium' } = options;
    
    // 对于大文件，建议服务端处理
    if (file.size > 100 * 1024 * 1024) {
      throw new Error('File too large for client-side compression. Consider server-side processing.');
    }
    
    // 使用 Web API 进行基础压缩
    return this.compressWithWebAPI(file, options);
  }
  
  async extractThumbnail(file: File, time: number = 1): Promise<File> {
    return new Promise((resolve, reject) => {
      const video = document.createElement('video');
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d')!;
      
      video.onloadedmetadata = () => {
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;
        video.currentTime = time;
      };
      
      video.onseeked = () => {
        ctx.drawImage(video, 0, 0);
        canvas.toBlob((blob) => {
          if (blob) {
            const thumbnail = new File([blob], 'thumbnail.jpg', {
              type: 'image/jpeg'
            });
            resolve(thumbnail);
          } else {
            reject(new Error('Failed to generate thumbnail'));
          }
        }, 'image/jpeg', 0.8);
      };
      
      video.src = URL.createObjectURL(file);
    });
  }
  
  private async compressWithWebAPI(file: File, options: VideoCompressOptions): Promise<File> {
    // 实现基于 WebCodecs API 的压缩（如果浏览器支持）
    // 或者使用 FFmpeg.wasm（需要考虑包大小和性能）
    // 这里是简化实现
    return file; // 暂时返回原文件
  }
}
```

### 状态管理
```typescript
interface UploadState {
  // 上传状态
  isUploading: boolean;
  progress: number;
  error?: string;
  
  // 文件状态
  files: FileInfo[];
  uploadedFiles: UploadedFile[];
  
  // UI状态
  isDragOver: boolean;
  isVisible: boolean;
}

// 状态更新模式
const useUploadState = () => {
  const [state, setState] = useState<UploadState>(initialState);
  
  // 使用不可变更新
  const updateState = (updates: Partial<UploadState>) => {
    setState(prev => ({ ...prev, ...updates }));
  };
  
  return { state, updateState };
};
```

## 🎨 样式系统

### Tailwind CSS 策略
```typescript
// 基础样式类
const baseClasses = {
  container: 'relative border-2 border-dashed rounded-lg transition-colors',
  dragActive: 'border-primary bg-primary/5',
  dragInactive: 'border-border hover:border-primary/50',
  disabled: 'opacity-50 cursor-not-allowed',
};

// 响应式设计
const responsiveClasses = {
  mobile: 'p-4 text-sm',
  tablet: 'p-6 text-base', 
  desktop: 'p-8 text-lg',
};

// 主题适配
const themeClasses = {
  light: 'bg-background text-foreground',
  dark: 'bg-background text-foreground',
};
```

### 动画效果
```css
/* 平滑过渡动画 */
.upload-container {
  transition: all 0.2s ease-in-out;
}

/* 拖拽反馈动画 */
.drag-over {
  animation: pulse 1s infinite;
}

/* 上传进度动画 */
.progress-bar {
  transition: width 0.3s ease-in-out;
}
```

## 🔄 扩展指南

### 专用预览组件设计

#### 不同文件类型的预览外观
```typescript
// previews/ImagePreview.tsx - 图片预览
export const ImagePreview: React.FC<PreviewProps> = ({ file, onRemove }) => {
  return (
    <div className="relative aspect-square bg-gray-100 rounded-lg overflow-hidden group">
      <img 
        src={file.previewUrl} 
        alt={file.fileName}
        className="w-full h-full object-cover transition-transform group-hover:scale-105"
      />
      
      {/* 压缩选项 */}
      <div className="absolute top-2 left-2 flex gap-1">
        <CompressionBadge quality={file.compressionQuality} />
        <SizeBadge original={file.originalSize} compressed={file.fileSize} />
      </div>
      
      {/* 操作按钮 */}
      <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <button onClick={() => onCompress(file)} className="p-1 bg-blue-500 text-white rounded">
          <Compress size={12} />
        </button>
        <button onClick={() => onRemove(file)} className="p-1 bg-red-500 text-white rounded ml-1">
          <X size={12} />
        </button>
      </div>
      
      {/* 文件信息 */}
      <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white p-2 opacity-0 group-hover:opacity-100 transition-opacity">
        <p className="text-xs truncate">{file.fileName}</p>
        <p className="text-xs">{formatFileSize(file.fileSize)} • {file.dimensions}</p>
      </div>
    </div>
  );
};

// previews/VideoPreview.tsx - 视频预览  
export const VideoPreview: React.FC<PreviewProps> = ({ file, onRemove }) => {
  const [thumbnail, setThumbnail] = useState<string>();
  const [duration, setDuration] = useState<number>();
  
  useEffect(() => {
    // 自动生成视频缩略图
    generateVideoThumbnail(file).then(setThumbnail);
    getVideoDuration(file).then(setDuration);
  }, [file]);
  
  return (
    <div className="relative aspect-video bg-gray-900 rounded-lg overflow-hidden group">
      {thumbnail && (
        <img 
          src={thumbnail} 
          alt={file.fileName}
          className="w-full h-full object-cover"
        />
      )}
      
      {/* 播放按钮 */}
      <div className="absolute inset-0 flex items-center justify-center">
        <div className="w-12 h-12 bg-black/60 rounded-full flex items-center justify-center">
          <Play size={24} className="text-white ml-1" />
        </div>
      </div>
      
      {/* 视频信息 */}
      <div className="absolute bottom-2 left-2 bg-black/60 text-white px-2 py-1 rounded text-xs">
        {formatDuration(duration)}
      </div>
      
      {/* 压缩选项 */}
      <div className="absolute top-2 left-2">
        <VideoCompressionBadge 
          originalSize={file.originalSize} 
          quality={file.compressionQuality}
        />
      </div>
    </div>
  );
};

// previews/DocumentPreview.tsx - 文档预览
export const DocumentPreview: React.FC<PreviewProps> = ({ file, onRemove }) => {
  const fileIcon = getDocumentIcon(file.mimeType);
  
  return (
    <div className="bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
      <div className="flex items-start gap-3">
        <div className="flex-shrink-0">
          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
            {fileIcon}
          </div>
        </div>
        
        <div className="flex-1 min-w-0">
          <h4 className="text-sm font-medium text-gray-900 truncate">{file.fileName}</h4>
          <p className="text-xs text-gray-500 mt-1">
            {file.mimeType.split('/')[1].toUpperCase()} • {formatFileSize(file.fileSize)}
          </p>
          
          {/* 文档预览（如果支持） */}
          {file.textPreview && (
            <p className="text-xs text-gray-600 mt-2 line-clamp-3">
              {file.textPreview}
            </p>
          )}
        </div>
        
        <div className="flex-shrink-0">
          <button 
            onClick={() => onRemove(file)}
            className="p-1 text-gray-400 hover:text-red-500 transition-colors"
          >
            <X size={16} />
          </button>
        </div>
      </div>
    </div>
  );
};

// previews/AudioPreview.tsx - 音频预览
export const AudioPreview: React.FC<PreviewProps> = ({ file, onRemove }) => {
  const [isPlaying, setIsPlaying] = useState(false);
  const [duration, setDuration] = useState<number>();
  const [waveformData, setWaveformData] = useState<number[]>();
  
  return (
    <div className="bg-gradient-to-r from-purple-100 to-pink-100 rounded-lg p-4">
      <div className="flex items-center gap-3">
        <button 
          onClick={() => setIsPlaying(!isPlaying)}
          className="w-10 h-10 bg-purple-500 text-white rounded-full flex items-center justify-center hover:bg-purple-600 transition-colors"
        >
          {isPlaying ? <Pause size={16} /> : <Play size={16} />}
        </button>
        
        <div className="flex-1">
          <h4 className="text-sm font-medium text-gray-900 truncate">{file.fileName}</h4>
          <div className="flex items-center gap-2 mt-1">
            <div className="flex-1 h-1 bg-gray-200 rounded-full overflow-hidden">
              <div className="h-full bg-purple-500 rounded-full" style={{ width: '30%' }} />
            </div>
            <span className="text-xs text-gray-500">{formatDuration(duration)}</span>
          </div>
        </div>
        
        <button 
          onClick={() => onRemove(file)}
          className="p-1 text-gray-400 hover:text-red-500 transition-colors"
        >
          <X size={16} />
        </button>
      </div>
      
      {/* 波形图（可选） */}
      {waveformData && (
        <div className="mt-3 h-8 flex items-end gap-1">
          {waveformData.map((height, index) => (
            <div 
              key={index} 
              className="bg-purple-300 w-1 rounded-full"
              style={{ height: `${height * 100}%` }}
            />
          ))}
        </div>
      )}
    </div>
  );
};
```

#### 添加新文件类型的完整流程
```typescript
// 1. 在 FILE_CATEGORIES 中定义新类型
export const FILE_CATEGORIES = {
  // ... 现有类型
  CODE: {
    types: ['text/javascript', 'text/typescript', 'text/css', 'text/html'],
    extensions: ['.js', '.ts', '.css', '.html', '.jsx', '.tsx'],
    maxSize: 5 * 1024 * 1024, // 5MB
    features: ['syntax-highlight', 'preview']
  }
} as const;

// 2. 创建专用预览组件
// previews/CodePreview.tsx
export const CodePreview: React.FC<PreviewProps> = ({ file }) => {
  const [content, setContent] = useState<string>();
  const language = detectLanguage(file.fileName);
  
  useEffect(() => {
    readFileAsText(file).then(setContent);
  }, [file]);
  
  return (
    <div className="bg-gray-900 rounded-lg overflow-hidden">
      <div className="bg-gray-800 px-4 py-2 flex items-center justify-between">
        <span className="text-sm text-gray-300">{file.fileName}</span>
        <span className="text-xs text-gray-400 uppercase">{language}</span>
      </div>
      <div className="p-4 max-h-48 overflow-auto">
        <pre className="text-sm text-gray-100">
          <code className={`language-${language}`}>
            {content?.slice(0, 500)}{content && content.length > 500 && '...'}
          </code>
        </pre>
      </div>
    </div>
  );
};

// 3. 注册到预览工厂
PreviewFactory.register(
  ['text/javascript', 'text/typescript', 'text/css', 'text/html'],
  new CodePreview()
);

// 4. 创建专用处理器（如果需要）
export class CodeProcessor implements FileProcessor {
  supports(file: File): boolean {
    return FILE_CATEGORIES.CODE.types.includes(file.type);
  }
  
  async process(file: File): Promise<ProcessResult> {
    const content = await readFileAsText(file);
    const language = detectLanguage(file.name);
    const lineCount = content.split('\n').length;
    
    return {
      success: true,
      metadata: { language, lineCount, size: file.size },
      preview: content.slice(0, 500)
    };
  }
}
```

### 压缩功能集成

#### 压缩选项UI组件
```typescript
// ui-components/CompressOptions.tsx
export const CompressOptions: React.FC<CompressOptionsProps> = ({ 
  fileType, 
  onOptionsChange,
  defaultOptions 
}) => {
  const [options, setOptions] = useState(defaultOptions);
  
  const updateOptions = (newOptions: Partial<CompressionOptions>) => {
    const updated = { ...options, ...newOptions };
    setOptions(updated);
    onOptionsChange(updated);
  };
  
  if (fileType === 'IMAGE') {
    return (
      <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-medium">图片压缩选项</h4>
        
        {/* 质量滑块 */}
        <div>
          <label className="block text-sm font-medium mb-2">
            压缩质量: {Math.round(options.quality * 100)}%
          </label>
          <input
            type="range"
            min="0.1"
            max="1"
            step="0.1"
            value={options.quality}
            onChange={(e) => updateOptions({ quality: parseFloat(e.target.value) })}
            className="w-full"
          />
        </div>
        
        {/* 尺寸限制 */}
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">最大宽度</label>
            <input
              type="number"
              value={options.maxWidth}
              onChange={(e) => updateOptions({ maxWidth: parseInt(e.target.value) })}
              className="w-full p-2 border rounded"
              placeholder="1920"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">最大高度</label>
            <input
              type="number"
              value={options.maxHeight}
              onChange={(e) => updateOptions({ maxHeight: parseInt(e.target.value) })}
              className="w-full p-2 border rounded"
              placeholder="1080"
            />
          </div>
        </div>
        
        {/* 格式转换 */}
        <div>
          <label className="block text-sm font-medium mb-2">输出格式</label>
          <select
            value={options.format}
            onChange={(e) => updateOptions({ format: e.target.value as ImageFormat })}
            className="w-full p-2 border rounded"
          >
            <option value="auto">自动选择</option>
            <option value="jpeg">JPEG</option>
            <option value="webp">WebP</option>
            <option value="png">PNG</option>
          </select>
        </div>
      </div>
    );
  }
  
  if (fileType === 'VIDEO') {
    return (
      <div className="space-y-4 p-4 bg-gray-50 rounded-lg">
        <h4 className="font-medium">视频压缩选项</h4>
        
        <div className="bg-yellow-50 border border-yellow-200 rounded p-3">
          <p className="text-sm text-yellow-800">
            ⚠️ 客户端视频压缩可能影响性能，大文件建议服务端处理
          </p>
        </div>
        
        {/* 压缩级别 */}
        <div>
          <label className="block text-sm font-medium mb-2">压缩级别</label>
          <select
            value={options.quality}
            onChange={(e) => updateOptions({ quality: e.target.value })}
            className="w-full p-2 border rounded"
          >
            <option value="low">低质量 (文件最小)</option>
            <option value="medium">中等质量 (推荐)</option>
            <option value="high">高质量 (文件较大)</option>
          </select>
        </div>
        
        {/* 分辨率选择 */}
        <div>
          <label className="block text-sm font-medium mb-2">目标分辨率</label>
          <select
            value={options.resolution}
            onChange={(e) => updateOptions({ resolution: e.target.value })}
            className="w-full p-2 border rounded"
          >
            <option value="auto">保持原分辨率</option>
            <option value="720p">720p (1280×720)</option>
            <option value="1080p">1080p (1920×1080)</option>
            <option value="480p">480p (854×480)</option>
          </select>
        </div>
        
        {/* 比特率 */}
        <div>
          <label className="block text-sm font-medium mb-2">
            比特率: {options.bitrate} kbps
          </label>
          <input
            type="range"
            min="500"
            max="5000"
            step="100"
            value={options.bitrate}
            onChange={(e) => updateOptions({ bitrate: parseInt(e.target.value) })}
            className="w-full"
          />
        </div>
      </div>
    );
  }
  
  return null;
};
```

#### 集成到专用上传器
```typescript
// specialized/ImageUploader.tsx (扩展版本)
export const ImageUploaderWithCompression: React.FC<ImageUploaderProps> = ({
  onFileUpload,
  enableCompression = false,
  defaultCompressionOptions,
  ...props
}) => {
  const [compressionOptions, setCompressionOptions] = useState(defaultCompressionOptions);
  const [showCompressionPanel, setShowCompressionPanel] = useState(false);
  
  const handleFileUpload = async (files: File[]) => {
    if (enableCompression && compressionOptions) {
      // 在上传前压缩文件
      const compressedFiles = await Promise.all(
        files.map(file => compressImage(file, compressionOptions))
      );
      onFileUpload(compressedFiles);
    } else {
      onFileUpload(files);
    }
  };
  
  return (
    <div className="space-y-4">
      <ImageUploader 
        {...props}
        onFileUpload={handleFileUpload}
      />
      
      {enableCompression && (
        <div>
          <button
            onClick={() => setShowCompressionPanel(!showCompressionPanel)}
            className="flex items-center gap-2 text-sm text-blue-600 hover:text-blue-700"
          >
            <Settings size={16} />
            压缩设置
          </button>
          
          {showCompressionPanel && (
            <CompressOptions
              fileType="IMAGE"
              onOptionsChange={setCompressionOptions}
              defaultOptions={compressionOptions}
            />
          )}
        </div>
      )}
    </div>
  );
};
```

### 性能优化策略

#### 客户端性能考虑
```typescript
// utils/performance-monitor.ts
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  
  static getInstance() {
    if (!this.instance) {
      this.instance = new PerformanceMonitor();
    }
    return this.instance;
  }
  
  // 检测设备性能
  assessDeviceCapability(): DeviceCapability {
    const memory = (navigator as any).deviceMemory || 4; // GB
    const cores = navigator.hardwareConcurrency || 4;
    const connection = (navigator as any).connection;
    
    return {
      canHandleLargeFiles: memory >= 8 && cores >= 4,
      canCompressVideo: memory >= 4 && cores >= 2,
      networkSpeed: connection?.effectiveType || '4g',
      recommendedMaxFileSize: memory >= 8 ? 100 * 1024 * 1024 : 50 * 1024 * 1024
    };
  }
  
  // 监控压缩性能
  async measureCompressionPerformance(file: File, compressFunction: Function): Promise<PerformanceMetrics> {
    const startTime = performance.now();
    const startMemory = (performance as any).memory?.usedJSHeapSize || 0;
    
    try {
      const result = await compressFunction(file);
      const endTime = performance.now();
      const endMemory = (performance as any).memory?.usedJSHeapSize || 0;
      
      return {
        duration: endTime - startTime,
        memoryUsed: endMemory - startMemory,
        compressionRatio: file.size / result.size,
        success: true
      };
    } catch (error) {
      return {
        duration: performance.now() - startTime,
        memoryUsed: 0,
        compressionRatio: 1,
        success: false,
        error: error.message
      };
    }
  }
}

// 智能压缩策略
export class AdaptiveCompressor {
  private performanceMonitor = PerformanceMonitor.getInstance();
  
  async compressFile(file: File, userOptions: CompressionOptions): Promise<File> {
    const capability = this.performanceMonitor.assessDeviceCapability();
    
    // 根据设备能力调整压缩策略
    const adaptedOptions = this.adaptOptions(userOptions, capability, file);
    
    // 对于大文件或低性能设备，使用渐进式压缩
    if (file.size > capability.recommendedMaxFileSize || !capability.canHandleLargeFiles) {
      return this.progressiveCompress(file, adaptedOptions);
    }
    
    // 标准压缩
    return this.standardCompress(file, adaptedOptions);
  }
  
  private adaptOptions(
    userOptions: CompressionOptions,
    capability: DeviceCapability,
    file: File
  ): CompressionOptions {
    const adapted = { ...userOptions };
    
    // 低性能设备降低质量要求
    if (!capability.canHandleLargeFiles) {
      adapted.quality = Math.min(adapted.quality, 0.7);
      adapted.maxWidth = Math.min(adapted.maxWidth, 1280);
      adapted.maxHeight = Math.min(adapted.maxHeight, 720);
    }
    
    // 大文件自动降级
    if (file.size > 50 * 1024 * 1024) {
      adapted.quality *= 0.8;
    }
    
    return adapted;
  }
  
  private async progressiveCompress(file: File, options: CompressionOptions): Promise<File> {
    // 分步骤压缩，避免阻塞UI
    let currentFile = file;
    
    // 第一步：尺寸压缩
    if (options.maxWidth || options.maxHeight) {
      currentFile = await this.resizeInChunks(currentFile, options);
    }
    
    // 第二步：质量压缩
    currentFile = await this.qualityCompressInChunks(currentFile, options);
    
    return currentFile;
  }
}
```

### 性能优化建议

1. **大文件处理**
```typescript
// 大文件检测和处理
const handleLargeFile = (file: File) => {
  const LARGE_FILE_THRESHOLD = 50 * 1024 * 1024; // 50MB
  
  if (file.size > LARGE_FILE_THRESHOLD) {
    // 启用分块上传
    return new ChunkedUploader(file);
  }
  
  return new StandardUploader(file);
};
```

2. **内存管理**
```typescript
// 清理预览URL避免内存泄漏
useEffect(() => {
  return () => {
    uploadedFiles.forEach(file => {
      if (file.previewUrl.startsWith('blob:')) {
        URL.revokeObjectURL(file.previewUrl);
      }
    });
  };
}, [uploadedFiles]);
```

3. **并发控制**
```typescript
// 限制并发上传数量
const MAX_CONCURRENT_UPLOADS = 3;

const uploadQueue = new UploadQueue(MAX_CONCURRENT_UPLOADS);
```

## 🧪 测试策略

### 单元测试
```typescript
// __tests__/FileUploader.test.tsx
describe('FileUploader', () => {
  it('should accept valid file types', () => {
    // 测试文件类型验证
  });
  
  it('should reject files exceeding size limit', () => {
    // 测试文件大小限制
  });
  
  it('should handle upload errors gracefully', () => {
    // 测试错误处理
  });
});
```

### 集成测试
```typescript
// __tests__/upload-integration.test.tsx
describe('Upload Integration', () => {
  it('should complete full upload workflow', async () => {
    // 测试完整上传流程
  });
  
  it('should handle network interruption', async () => {
    // 测试网络中断恢复
  });
});
```

### 性能测试
```typescript
// __tests__/upload-performance.test.tsx
describe('Upload Performance', () => {
  it('should handle multiple large files', async () => {
    // 测试多文件上传性能
  });
  
  it('should not cause memory leaks', () => {
    // 测试内存泄漏
  });
});
```

## 📚 使用示例

### 基础图片上传
```typescript
<ImageUploader
  onFileUpload={handleFileUpload}
  uploadedFiles={uploadedFiles}
  onFileRemove={handleFileRemove}
  onContainerWidthChange={handleUploaderWidthChange} // 动态布局支持
  maxImages={5}
  parentHeight={220}
  userUuid={user?.uuid}
/>
```

### 多类型文件上传
```typescript
<FileUploader
  acceptMimeList={[...IMAGE_TYPES, ...DOCUMENT_TYPES]}
  maxFileCount={10}
  maxFileSize={50 * 1024 * 1024} // 50MB
  userUuid={user?.uuid}
  onUploadComplete={handleUploadComplete}
  onFileRemove={handleFileRemove} // 文件删除支持
  enableChunkedUpload={true}
  enableCompression={true}
/>
```

### 自定义样式上传
```typescript
<FileUploader
  variant="compact"
  className="custom-uploader"
  style={{ 
    borderRadius: '12px',
    background: 'linear-gradient(...)' 
  }}
  onUploadComplete={handleUploadComplete}
/>
```

## 🔮 未来规划

### 短期目标 (1-2个月)
- [x] **智能清理机制** ✅ 已完成
  - [x] useFileCleanup自定义Hook实现
  - [x] 延迟清理策略（删除时记录意图）
  - [x] 任务提交时清理已删除文件
  - [x] 表单清空时清理所有文件
  - [x] 图片和视频生成器集成清理功能

- [x] **动态布局系统** ✅ 已完成  
  - [x] 容器宽度智能扩展（216px -> 328px）
  - [x] 2×2和2×3网格自动切换
  - [x] 父子组件宽度变化通信
  - [x] Image Fusion模式布局优化

- [ ] **预览系统完善**
  - [ ] 视频缩略图生成和播放预览
  - [ ] 音频波形图显示和播放控件
  - [ ] 文档首页预览和文本提取
  - [ ] 代码文件语法高亮预览
  
- [ ] **压缩功能基础**
  - [ ] 图片客户端压缩（Canvas API）
  - [ ] 压缩选项UI组件
  - [ ] 压缩前后对比显示
  - [ ] 批量压缩进度管理

### 中期目标 (3-6个月)
- [ ] **高级压缩特性**
  - [ ] 视频压缩（WebCodecs API / FFmpeg.wasm）
  - [ ] 智能压缩策略（设备性能自适应）
  - [ ] 格式转换支持（JPEG/WebP/AVIF）
  - [ ] 压缩质量预览

- [ ] **架构优化**
  - [ ] 处理器插件化架构
  - [ ] 多上传策略支持（预签名/服务器/混合）
  - [ ] 更完善的错误恢复机制
  - [ ] 性能监控和优化

### 长期目标 (6个月以上)
- [ ] **智能化特性**
  - [ ] AI驱动的文件内容分析
  - [ ] 智能压缩参数推荐
  - [ ] 自动文件分类和标签
  - [ ] 内容相似度检测

- [ ] **企业级功能**
  - [ ] 多租户支持
  - [ ] 权限和访问控制
  - [ ] 审计日志和合规性
  - [ ] 大规模文件处理

---

## 🔌 解耦设计总结

### 核心解耦策略
1. **接口层抽象** - 通过 Adapter 模式隔离具体实现，便于切换上传服务商
2. **处理器分离** - 文件处理逻辑独立于UI组件，支持插件化扩展
3. **预览工厂** - 预览组件通过工厂模式注册，新文件类型零侵入式添加
4. **配置驱动** - 文件类型定义、验证规则、压缩参数均可外部配置
5. **事件通信** - 组件间通过回调函数通信，避免紧耦合依赖

### 后端依赖最小化
- **仅需要3个接口**：上传预签名、上传确认、文件删除
- **标准HTTP协议**：不依赖特定后端技术栈
- **可插拔存储**：支持多种云存储服务（S3、OSS、R2等）
- **客户端处理优先**：压缩、预览、验证等在前端完成

### 扩展便利性
- **新文件类型**：只需定义类型配置和预览组件，无需修改核心代码
- **新功能特性**：通过接口实现，现有代码无感知升级
- **性能优化**：通过策略模式适配不同设备性能，用户体验自动优化

---

## 🎯 最新实现亮点

### 智能文件清理系统 ✅
- **延迟清理策略** - 用户删除文件时先记录意图，允许后悔操作
- **批量API调用** - 减少网络请求，提高清理效率
- **统一Hook接口** - `useFileCleanup` 提供一致的清理体验
- **多场景适配** - 支持单文件删除、任务提交清理、表单清空清理
- **容错处理** - 清理失败不影响主要功能，仅记录警告日志

### 动态布局系统 ✅  
- **智能宽度扩展** - 根据文件数量自动调整容器尺寸（216px ↔ 328px）
- **网格布局切换** - 2×2布局（2-3张图片）vs 2×3布局（4-5张图片）
- **父子组件通信** - 通过回调通知父组件调整整体布局
- **无缝体验** - 布局变化平滑过渡，用户体验自然

### 技术价值
- **数据一致性** - 确保前端状态与后端存储完全同步
- **用户体验** - 操作响应及时，视觉反馈直观
- **代码质量** - 符合项目规范，TypeScript类型安全，英文注释详细
- **扩展性** - 为其他文件类型和功能奠定了坚实基础

## 📝 贡献指南

1. **代码规范** - 遵循项目的TypeScript和代码风格规范
2. **测试覆盖** - 新功能必须包含相应的测试用例
3. **文档更新** - 更新相关的API文档和使用示例
4. **性能考虑** - 确保新功能不影响现有性能
5. **向后兼容** - 保持API的向后兼容性
6. **解耦原则** - 新功能应通过接口和配置实现，避免硬编码依赖

