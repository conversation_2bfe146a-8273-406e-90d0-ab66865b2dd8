/**
 * NgrokWebHook
 * 
 * Utility for interacting with locally running ngrok instances
 * Used in development environment to expose local servers to the internet
 * for webhook callbacks from external services like KIE
 */

/**
 * Get the public URL from a running ngrok instance
 * Caches the result to avoid repeated API calls
 */
export class NgrokWebHook {
  private static ngrokUrl: string | null = null;
  private static envVarNameOfBaseUrl: string = "NEXT_PUBLIC_WEB_URL";
  
  /**
   * Get the public URL from a running ngrok instance
   * Attempts to connect to the ngrok API running locally on port 4040
   * 
   * @returns The public ngrok URL, or null if not available
   */
  static async getPublicUrl(): Promise<string | null> {
    // Return cached URL if available to avoid repeated API calls
    if (NgrokWebHook.ngrokUrl) {
      return NgrokWebHook.ngrokUrl;
    }
    
    try {
      // Try to connect to the ngrok API
      const response = await fetch('http://127.0.0.1:4040/api/tunnels');
      
      if (!response.ok) {
        console.warn('Failed to connect to ngrok API:', response.status, response.statusText);
        return null;
      }
      
      const data = await response.json();
      
      // Find the HTTPS tunnel (preferred)
      const httpsUrl = data.tunnels?.find(
        (tunnel: any) => tunnel.proto === 'https'
      )?.public_url;
      
      if (httpsUrl) {
        console.log(`Found ngrok HTTPS URL: ${httpsUrl}`);
        NgrokWebHook.ngrokUrl = httpsUrl;
        return httpsUrl;
      }
      
      // Fall back to HTTP if HTTPS is not available
      const httpUrl = data.tunnels?.find(
        (tunnel: any) => tunnel.proto === 'http'
      )?.public_url;
      
      if (httpUrl) {
        console.log(`Found ngrok HTTP URL: ${httpUrl}`);
        NgrokWebHook.ngrokUrl = httpUrl;
        return httpUrl;
      }
      
      console.warn('No active ngrok tunnels found');
      return null;
    } catch (error) {
      console.error('Error checking ngrok status:', error);
      return null;
    }
  }

  /**
   * Normalize a URL by ensuring it doesn't have a trailing slash
   * This helps prevent double slashes when concatenating with paths
   * 
   * @param url The URL to normalize
   * @returns Normalized URL without trailing slash
   */
  private static normalizeBaseUrl(url: string): string {
    return url.endsWith('/') ? url.slice(0, -1) : url;
  }

  /**
   * Build a webhook URL for receiving callbacks
   * Uses ngrok in development, or configured public URL in production
   * 
   * @param path The API route path (should start with /)
   * @returns Full URL for the webhook callback
   */
  static async getWebhookUrl(path: string): Promise<string> {
    // Strip leading slash if present in the path
    const cleanPath = path.startsWith('/') ? path : `/${path}`;
    
    // In development, try to use ngrok
    if (process.env.NODE_ENV === 'development') {
      const ngrokUrl = await NgrokWebHook.getPublicUrl();
      
      if (ngrokUrl) {
        // Normalize ngrok URL to prevent double slashes
        const normalizedNgrokUrl = NgrokWebHook.normalizeBaseUrl(ngrokUrl);
        return `${normalizedNgrokUrl}${cleanPath}`;
      }else{
        throw new Error('No ngrok tunnel found for development webhook.');
      }
    }
    
    // Use environment variable (with customizable name if provided)
    const baseUrl = process.env[NgrokWebHook.envVarNameOfBaseUrl];
    
    if (!baseUrl) {
      throw new Error(`Environment variable ${NgrokWebHook.envVarNameOfBaseUrl} is not set`);
    }
    
    // Normalize environment variable URL to prevent double slashes
    const normalizedBaseUrl = NgrokWebHook.normalizeBaseUrl(baseUrl);
    return `${normalizedBaseUrl}${cleanPath}`;
  }
  
  /**
   * Reset the cached ngrok URL
   * Useful if the ngrok service is restarted
   */
  static resetCache(): void {
    NgrokWebHook.ngrokUrl = null;
    console.log('Ngrok URL cache has been reset');
  }
}
