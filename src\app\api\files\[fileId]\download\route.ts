import { NextRequest, NextResponse } from 'next/server';
import { auth } from '@/auth';
import { getPresignedDownloadUrl, getFileRecord } from '@/services/file';
import { Session } from 'next-auth';
import logger from '@/lib/logger';
import { getUserUuid } from '@/services/user';

/**
 * GET /api/files/{fileId}/download
 * @description Generates a secure, temporary presigned URL to force a file download.
 * @param {Request} request - The incoming request object.
 * @param {object} params - The route parameters.
 * @param {string} params.fileId - The unique ID of the file to download.
 * @returns {NextResponse} A JSON response with the presigned `downloadUrl` or an error.
 */
export async function GET(
  req: NextRequest,
  { params }: { params: Promise<{ fileId: string }> }
) {
  // const session = await auth() as Session;
  const userUuid = await getUserUuid();
  // 如果没有登录
  if (!userUuid) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { fileId } = await params;
  if (!fileId) {
    return NextResponse.json({ error: 'File ID is required' }, { status: 400 });
  }

  // Authorization: Check if the user owns the file before generating the URL
  const fileRecord = await getFileRecord(fileId);
  if (!fileRecord || fileRecord.user_uuid !== userUuid) {
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }

  logger.info('Downloading file via presigned URL', 
    { fileId, userUuid, storageKey: fileRecord.storage_key }, 
    {
      filePath: "app/api/files/[fileId]/download/route.ts",
      functionName: 'GET'
    });

  // 未来还有可能不需要校验用户登录和文件所有权，但是需要验证文件是否存在 ---------------

  try {
    const downloadUrl = await getPresignedDownloadUrl(fileId);

    if (!downloadUrl) {
      return NextResponse.json({ error: 'File not found or unable to generate URL' }, { status: 404 });
    }

    return NextResponse.json({ downloadUrl });
  } catch (error) {
    logger.error('Failed to create presigned download URL', 
      error, 
      { fileId, userUuid, storageKey: fileRecord.storage_key },
      {
        filePath: "app/api/files/[fileId]/download/route.ts",
        functionName: 'GET'
      });
    return NextResponse.json({ error: 'Internal Server Error' }, { status: 500 });
  }
}
