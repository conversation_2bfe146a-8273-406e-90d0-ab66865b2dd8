/**
 * KIE AI Model Provider
 * 
 * This module exports all KIE-related model providers.
 * Main exports include:
 * - KieImageGenerator: For generating images using KIE's GPT-4o image generation API
 */

import ImageGenerator, { Models as ImageModels } from './image-generator';

// Default export for convenience
export default {
  ImageGenerator: ImageGenerator,
  ImageModels: ImageModels
};

/*
This module provides a unified interface for interacting with KIE AI's image generation APIs.
Similar to the Replicate module, it offers:

1. Image generation capabilities through multiple models
2. Authentication handling
3. Result polling
4. Error handling

Usage example:
```typescript
import KieAI from '@/aisdk/model-providers/kie';

// Initialize image generator
const generator = new KieAI.ImageGenerator();

// Generate image
const imageUrl = await generator.generateImage('gpt4o', {
  prompt: 'A beautiful sunset over the mountains',
  size: '1:1'
});
```
*/ 