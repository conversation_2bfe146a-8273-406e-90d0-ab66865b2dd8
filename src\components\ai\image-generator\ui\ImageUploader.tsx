"use client";

import { useTranslations } from 'next-intl';
import Image from 'next/image';
import { useState, useMemo, useEffect } from 'react';
import { XCircle, Plus, Upload } from 'lucide-react';
import { FileUploader } from '@/components/ui/file-uploader';
import { UploadResult } from '@/types/file-upload';
import { FileInfo, FileCategory } from '@/types/file';
import { cn } from '@/lib/utils';

interface UploadedFile extends FileInfo {
  /** Local blob URL for immediate preview */
  previewUrl: string;
}

interface ImageUploaderProps {
  /** Callback when files are uploaded */
  onFileUpload: (files: UploadedFile[]) => void;
  className?: string;
  /** Currently uploaded files */
  uploadedFiles?: UploadedFile[];
  /** Called when user removes an uploaded file */
  onFileRemove?: (index: number) => void;
  /** Maximum number of images allowed (1-9) */
  maxImages?: number;
  /** Custom inline styles for the container */
  style?: React.CSSProperties;
  parentHeight: number;
  /** User UUID for authentication */
  userUuid?: string;
  /** Callback to report container width changes */
  onContainerWidthChange?: (width: number) => void;
}

/**
 * 🔲 Compact Upload Button Component
 * 
 * This is a specialized version of FileUploader designed to fit seamlessly
 * into grid layouts. It's the small "+" button you see in the grid when
 * users can upload more files.
 * 
 * Key Features:
 * - **Grid Integration**: Same size as image previews
 * - **Compact Design**: Shows only essential UI (+ icon)
 * - **Consistent Behavior**: Same upload logic as main uploader
 * - **Smart Limits**: Respects remaining upload slots
 * 
 * Design Philosophy:
 * - Don't overwhelm the grid with progress bars or status text
 * - Maintain visual harmony with image previews
 * - Provide clear affordance (+ icon indicates "add more")
 */
interface CompactUploaderProps {
  onUploadComplete: (results: UploadResult[], files: File[]) => void;  // Success callback
  onUploadError?: (error: string) => void;                            // Error callback
  userUuid?: string;                                                  // User authentication
  disabled?: boolean;                                                 // Disable uploads
  remainingSlots: number;                                             // How many more files allowed
  className?: string;                                                 // Additional styling
}

function CompactUploader({
  onUploadComplete,     // Handle successful uploads
  onUploadError,        // Handle upload errors
  userUuid,             // User authentication for upload service
  disabled = false,     // Can disable for loading states
  remainingSlots,       // Limit uploads to remaining slots
  className             // Additional CSS classes
}: CompactUploaderProps) {
  const t = useTranslations('components.image_generator.core');

  return (
    <div className={cn("aspect-square", className)}>
      <FileUploader
        acceptMimeList={['image/jpeg', 'image/png', 'image/gif', 'image/webp']}  // Same as main uploader
        maxFileCount={remainingSlots}        // Only upload as many as we have room for
        maxFileSize={10 * 1024 * 1024}      // 10MB limit (consistent with main uploader)
        userUuid={userUuid}                 // Pass through authentication
        onUploadComplete={onUploadComplete} // Forward success events
        onUploadError={onUploadError}       // Forward error events
        variant="compact"                   // Use compact variant (minimal UI)
        className="h-full w-full"           // Fill the grid square
      />
    </div>
  );
}

/**
 * 🧮 Dynamic Layout Configuration Calculator
 * 
 * This function is the heart of our adaptive layout system. It calculates the optimal
 * layout configuration based on the current state and requirements.
 * 
 * Key Design Principles:
 * 1. **Progressive Enhancement**: Start with simple single layout, then enhance to grid
 * 2. **User-Centric Design**: Show large upload area when no files for better UX
 * 3. **Responsive Growth**: Container expands horizontally as content grows
 * 4. **Consistent Experience**: Maintain visual harmony across different states
 * 
 * Layout Evolution:
 * - 0 files: Large single upload area (best first impression)
 * - 1-3 files: 2×2 grid (compact but spacious)
 * - 4-5 files: 2×3 grid (expand horizontally to accommodate more content)
 * 
 * @param maxImages - Maximum number of images allowed (affects grid planning)
 * @param currentCount - Current number of uploaded files (drives layout mode)
 * @param parentHeight - Available height from parent container (ensures fit)
 * @returns LayoutConfig object with calculated dimensions and CSS classes
 */
function getLayoutConfig(maxImages: number, currentCount: number, parentHeight: number) {
  // 🎯 Initial State: Show large upload area when no files
  // This provides the best first impression - a prominent, inviting upload zone
  // Users can easily see where to drag/click their first files
  if (currentCount === 0) {
    return {
      mode: 'single' as const,           // Single large uploader mode
      gridClass: '',                     // No grid classes needed
      maxWidth: Math.max(210, parentHeight - 4),      // Container width
      itemSize: Math.max(210, parentHeight - 4),      // Single item size
      containerWidth: Math.max(210, parentHeight - 4) // Actual container width
    };
  }

  // 🖼️ Single Image Mode: Maintain classic left-right layout
  // For single image scenarios (like Image-to-Image), keep the traditional layout
  // This preserves the familiar horizontal arrangement with prompt input on the right
  if (maxImages === 1) {
    return {
      mode: 'single' as const,           // Keep single mode for consistency
      gridClass: '',                     // No grid layout needed
      maxWidth: Math.max(210, parentHeight - 4),      // Fixed width
      itemSize: Math.max(210, parentHeight - 4),      // Same as container
      containerWidth: Math.max(210, parentHeight - 4) // No expansion needed
    };
  }

  // 🔲 Multi-Image Grid Mode: Intelligent layout expansion
  // This is where the magic happens - we calculate the optimal grid layout
  // based on how many files the user has and how many more they can add
  
  let gridClass: string;      // CSS Grid class (grid-cols-2 or grid-cols-3)
  let maxWidth: number;       // Legacy width for compatibility
  let itemSize: number;       // Size of each preview square
  let containerWidth: number; // Actual container width (this is key!)

  // 📊 Calculate total slots needed: existing files + upload button (if room for more)
  const totalSlots = currentCount + (currentCount < maxImages ? 1 : 0);
  
  // 📐 Base dimensions calculation
  const baseSize = Math.max(210, parentHeight - 4); // Minimum 210px, or fit parent
  const gap = 8; // Tailwind gap-2 = 8px spacing between items
  
  if (totalSlots <= 4) {
    // 🟦 2×2 Grid Strategy: Fit everything in original space
    // Perfect for 2-3 images + upload button, maintains compact layout
    gridClass = 'grid-cols-2';
    maxWidth = baseSize;                              // Keep original width
    itemSize = Math.floor((baseSize - gap) / 2);     // Each item gets half space minus gap
    containerWidth = baseSize;                        // Container stays same size
  } else {
    // 🟩 2×3 Grid Strategy: Expand horizontally for more content
    // When we need more space, grow the container to the right
    // This is the key innovation - the container grows but items stay same size!
    gridClass = 'grid-cols-3';
    const itemSizeFrom2x2 = Math.floor((baseSize - gap) / 2);  // Keep same item size as 2×2
    itemSize = itemSizeFrom2x2;
    maxWidth = itemSize * 3 + gap * 2;               // Calculate width for 3 columns
    containerWidth = maxWidth;                        // Container expands to fit content
  }

  // 🐛 Debug logging for development (remove in production)
  console.log('🎯 Layout Debug:', {
    maxImages,        // Maximum allowed images
    currentCount,     // Current number of files
    parentHeight,     // Available height
    totalSlots,       // Total grid slots needed
    baseSize,         // Base container size
    gridClass,        // CSS grid class applied
    maxWidth,         // Legacy max width
    itemSize,         // Individual item size
    containerWidth    // Final container width (this affects parent layout!)
  });

  return {
    mode: 'grid' as const,
    gridClass,
    maxWidth,
    itemSize,
    containerWidth
  };
}

/**
 * 🖼️ Modern Image Uploader Component
 * 
 * This is an intelligent, adaptive image uploader that grows with user needs.
 * 
 * Key Features for Beginners:
 * 1. **Smart Layout**: Automatically switches between single and grid layouts
 * 2. **Progressive Enhancement**: Starts simple, becomes powerful as needed
 * 3. **Responsive Design**: Adapts to different screen sizes and containers
 * 4. **Real-time Communication**: Tells parent when container size changes
 * 5. **User-Friendly**: Drag & drop, click to upload, preview with removal
 * 
 * How it works:
 * - No files: Shows large, inviting upload area
 * - 1-3 files: Switches to 2×2 grid (compact but roomy)
 * - 4-5 files: Expands to 2×3 grid (grows horizontally)
 * - Upload button integrates seamlessly into the grid
 * 
 * State Management:
 * - Uses React hooks for local state (error messages, loading states)
 * - Communicates with parent via callbacks (file changes, width changes)
 * - Manages file previews with blob URLs (memory efficient)
 * 
 * Performance Considerations:
 * - Dynamic layout calculations with useMemo (prevent unnecessary recalculations)
 * - Blob URL cleanup to prevent memory leaks
 * - Optimized re-renders with proper dependency arrays
 */
export default function ImageUploader({ 
  onFileUpload,           // Callback when files are successfully uploaded
  className,              // Additional CSS classes for styling
  uploadedFiles = [],     // Current files (controlled component pattern)
  onFileRemove,           // Callback when user removes a file
  maxImages = 9,          // Maximum allowed images (affects layout planning)
  style,                  // Inline styles for container
  parentHeight,           // Available height from parent (used for sizing)
  userUuid,               // User authentication for upload service
  onContainerWidthChange  // NEW: Callback to inform parent of width changes
}: ImageUploaderProps) {
  // 🌐 Internationalization: Get translated text for UI
  const t = useTranslations('components.image_generator.core');
  
  // 🚨 Error State: Track upload errors to show user feedback
  const [error, setError] = useState<string | null>(null);

  // 🔒 Input Validation: Ensure maxImages is within reasonable bounds
  // This prevents layout issues and performance problems
  const validMaxImages = Math.min(Math.max(1, maxImages), 9);

  // 🧮 Layout Calculation: Use useMemo to prevent unnecessary recalculations
  // This is a performance optimization - only recalculate when dependencies change
  const layoutConfig = useMemo(() => 
    getLayoutConfig(validMaxImages, uploadedFiles.length, parentHeight),
    [validMaxImages, uploadedFiles.length, parentHeight]  // Only recalc when these change
  );

  // 📏 Extract container width for parent communication
  const containerWidth = layoutConfig.containerWidth;

  // 📡 Parent Communication: Notify parent when our width changes
  // This allows the parent layout to adapt (e.g., adjust prompt input width)
  useEffect(() => {
    if (onContainerWidthChange) {
      onContainerWidthChange(containerWidth);
    }
  }, [containerWidth, onContainerWidthChange]);

  // ✅ Handle Successful File Upload
  // This function is called when the FileUploader component successfully uploads files
  // It processes the results and creates preview URLs for immediate display
  const handleUploadComplete = (results: UploadResult[], files: File[]) => {
    console.log('Files uploaded successfully:', results);
    setError(null); // Clear any previous errors
    
    // 🏗️ Transform upload results into our internal file format
    const newFiles: UploadedFile[] = [];
    
    results.forEach((result, index) => {
      if (result.success) {
        newFiles.push({
          // Server-provided metadata
          fileId: result.fileId,
          fileName: result.fileName,
          fileSize: result.fileSize,
          mimeType: result.mimeType,
          category: FileCategory.USER_UPLOADS,
          url: '', // Server URL will be provided later
          
          // 🖼️ Create local preview URL for immediate display
          // This allows users to see their images instantly without waiting for server processing
          previewUrl: URL.createObjectURL(files[index]),
        });
      }
    });
    
    // 🔄 File Replacement Strategy
    // Single image mode: Replace existing (like Image-to-Image)
    // Multi-image mode: Append to existing collection (like Image Fusion)
    const finalFiles = validMaxImages === 1 ? newFiles : [...uploadedFiles, ...newFiles];
    
    // 📡 Notify parent component about the new file state
    onFileUpload(finalFiles);
  };

  // ❌ Handle Upload Errors
  // When something goes wrong, we show the user a helpful error message
  const handleUploadError = (error: string) => {
    console.error('Upload failed:', error);
    setError(error); // Store error for display in UI
  };

  // 🗑️ Handle File Removal
  // When user clicks the X button on an image preview
  const handleRemoveFile = (index: number, e: React.MouseEvent) => {
    e.preventDefault();   // Don't trigger other click events
    e.stopPropagation();  // Don't bubble up to parent elements
    
    // 📡 Tell parent component to remove this file from their state
    if (onFileRemove) {
      onFileRemove(index);
    }
    
    // 🧹 Clear any error messages when user takes action
    setError(null);
  };

  // 🧮 Calculate how many more files user can upload
  const remainingSlots = validMaxImages - uploadedFiles.length;

  // 🎨 RENDER: Single Image Mode
  // This preserves the classic horizontal layout used in Image-to-Image mode
  // The upload area and prompt input sit side by side
  if (layoutConfig.mode === 'single') {
    return (
      <div className={cn("h-full flex flex-col items-center justify-center", className)} style={style}>
        {/* 🎯 Conditional Rendering: Show uploader OR preview, never both */}
        {uploadedFiles.length === 0 ? (
          // 📤 No Files State: Show large, inviting upload area
          <div 
            className="flex-shrink-0"
            style={{ 
              height: `${layoutConfig.maxWidth}px`,
              width: `${layoutConfig.maxWidth}px`
            }}
          >
            <FileUploader
              acceptMimeList={['image/jpeg', 'image/png', 'image/gif', 'image/webp']}
              maxFileCount={validMaxImages}
              maxFileSize={10 * 1024 * 1024} // 10MB limit
              userUuid={userUuid}
              onUploadComplete={handleUploadComplete}
              onUploadError={handleUploadError}
              variant="large"  // Large variant hides progress to avoid UI crowding
              className="h-full w-full"
            />
          </div>
        ) : (
          // 🖼️ File Preview State: Show the uploaded image with controls
          <div 
            className="relative group overflow-hidden rounded border border-border bg-muted"
            style={{ 
              width: `${layoutConfig.maxWidth}px`,
              height: `${layoutConfig.maxWidth}px`
            }}
          >
            {/* 🖼️ Image Preview with Next.js Image component for optimization */}
            <Image
              src={uploadedFiles[0].previewUrl}  // Use blob URL for instant preview
              alt={uploadedFiles[0].fileName}
              fill                               // Fill the container
              className="object-cover transition-transform group-hover:scale-105"  // Smooth hover effect
              sizes="220px"                      // Tell Next.js expected size for optimization
              unoptimized={true}                 // Skip optimization for blob URLs
            />
            
            {/* ❌ Remove Button: Only visible on hover */}
            <button
              onClick={(e) => handleRemoveFile(0, e)}
              className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600"
              aria-label={t('actions.remove') || 'Remove image'}  // Accessibility
            >
              <XCircle size={16} />
            </button>
            
            {/* 📊 File Info Overlay: Shows file details on hover */}
            <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white p-2 opacity-0 group-hover:opacity-100 transition-opacity">
              <p className="text-sm truncate">{uploadedFiles[0].fileName}</p>
              <p className="text-xs text-gray-300">
                {(uploadedFiles[0].fileSize / 1024 / 1024).toFixed(1)} MB
              </p>
            </div>
          </div>
        )}

        {/* Error display */}
        {error && (
          <div className="flex-shrink-0 mt-2 p-2 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded text-xs text-red-600 dark:text-red-400 max-w-full text-center">
            {error}
          </div>
        )}
      </div>
    );
  }

  // 🔲 RENDER: Multi-Image Grid Mode  
  // This is the magic layout that adapts from 2×2 to 2×3 as content grows
  return (
    <div className={cn("h-full flex flex-col items-center justify-center", className)} style={style}>
      {/* 🏗️ Grid Container: Dynamic width based on content */}
      <div 
        className={cn("grid gap-2", layoutConfig.gridClass)}  // Dynamic grid classes
        style={{ width: `${layoutConfig.containerWidth}px` }}  // KEY: Dynamic width!
      >
        {/* 🖼️ Existing Image Previews: Map over uploaded files */}
        {uploadedFiles.map((file, index) => (
          <div
            key={file.fileId}  // React key for efficient re-rendering
            className="relative group overflow-hidden rounded border border-border bg-muted aspect-square"
            style={{ 
              width: `${layoutConfig.itemSize}px`,   // Calculated size from layout config
              height: `${layoutConfig.itemSize}px`   // Always square for grid consistency
            }}
          >
            {/* 🖼️ Grid Image Preview: Smaller version for grid layout */}
            <Image
              src={file.previewUrl}                        // Blob URL for instant preview
              alt={file.fileName}
              fill                                          // Fill the square container
              className="object-cover transition-transform group-hover:scale-105"  // Hover zoom effect
              sizes={`${layoutConfig.itemSize}px`}         // Tell Next.js the actual size
              unoptimized={true}                           // Skip optimization for blob URLs
            />
            
            {/* ❌ Remove Button: Smaller for grid layout */}
            <button
              onClick={(e) => handleRemoveFile(index, e)}
              className="absolute top-1 right-1 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity hover:bg-red-600"
              aria-label={t('actions.remove') || 'Remove image'}
            >
              <XCircle size={12} />  {/* Smaller icon for grid items */}
            </button>
            
            {/* 📊 Compact File Info: Smaller text for grid layout */}
            <div className="absolute bottom-0 left-0 right-0 bg-black/60 text-white p-1 opacity-0 group-hover:opacity-100 transition-opacity">
              <p className="text-xs truncate">{file.fileName}</p>
              <p className="text-xs text-gray-300">
                {(file.fileSize / 1024 / 1024).toFixed(1)} MB
              </p>
            </div>
          </div>
        ))}

        {/* ➕ Compact Upload Button: Seamlessly integrated into grid */}
        {remainingSlots > 0 && (
          <div 
            style={{ 
              width: `${layoutConfig.itemSize}px`,   // Same size as image previews
              height: `${layoutConfig.itemSize}px`   // Perfect grid integration
            }}
          >
            <CompactUploader
              onUploadComplete={handleUploadComplete}  // Same handler as main uploader
              onUploadError={handleUploadError}        // Consistent error handling
              userUuid={userUuid}                      // User authentication
              remainingSlots={remainingSlots}          // How many more files allowed
            />
          </div>
        )}
      </div>

      {/* Error display */}
      {error && (
        <div className="flex-shrink-0 mt-2 p-2 bg-red-50 dark:bg-red-950/20 border border-red-200 dark:border-red-800 rounded text-xs text-red-600 dark:text-red-400 max-w-full text-center">
          {error}
        </div>
      )}

      {/* Empty state hint when no images and no uploader visible */}
      {uploadedFiles.length === 0 && remainingSlots === 0 && (
        <div className="flex-1 flex items-center justify-center text-center text-muted-foreground">
          <div>
            <p className="text-sm">
              {t('upload.instructions') || '点击上方区域上传图片'}
            </p>
          </div>
        </div>
      )}
    </div>
  );
}
