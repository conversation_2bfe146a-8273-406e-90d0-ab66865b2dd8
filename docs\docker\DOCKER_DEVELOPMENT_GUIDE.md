# Docker开发环境完整指南

## 🚀 快速开始

### 1. 配置Cloudflare认证

#### 获取API Token

1. 访问 [Cloudflare API Tokens](https://dash.cloudflare.com/profile/api-tokens)
2. 点击 "Create Token" → "Custom token"
3. **Token名称**: `shipany-stwd-docker-dev-token`
4. **设置权限**：
   - **Zone:Zone:Read**
   - **Zone:Zone Settings:Edit**
   - **Account:Workers Scripts:Edit**
   - **Account:Account Settings:Read**
5. **资源范围**：
   - **Account Resources**: All accounts（或选择特定账户）
   - **Zone Resources**: All zones（或选择特定域名）
6. 点击 "Continue to summary" → "Create Token"
7. 复制生成的Token（只显示一次，请妥善保存）

#### 获取Account ID

1. 访问 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. **从地址栏获取**（最可靠的方法）：
   - 地址栏格式：`https://dash.cloudflare.com/[ACCOUNT_ID]/...`
   - 示例：`https://dash.cloudflare.com/f2aa37b343ab10dae3dd8a6b6f98258a/home/<USER>
   - Account ID = `f2aa37b343ab10dae3dd8a6b6f98258a`

#### 配置环境变量

```bash
# 编辑 .env.docker 文件
CLOUDFLARE_API_TOKEN=your_api_token_here
CLOUDFLARE_ACCOUNT_ID=your_account_id_here
```

**示例配置**：

```bash
# 实际配置示例
CLOUDFLARE_API_TOKEN=uc1ljuROJ8Wim9xcsMAn3j6LCglTOaaxXaeQ0xA-
CLOUDFLARE_ACCOUNT_ID=f2aa37b343ab10dae3dd8a6b6f98258a
```

### 2. 启动开发环境

```bash
# 启动Docker容器（后台运行）
docker-compose up -d

# 查看容器状态
docker-compose ps
```

### 3. 进入开发容器

```bash
# 进入开发容器
docker-compose exec dev bash
```

### 4. 在容器内开发

```bash
# 启动Next.js开发服务器
pnpm dev

# 构建项目
pnpm build

# Cloudflare预览部署
pnpm cf:preview

# Cloudflare生产部署
pnpm cf:deploy

# 查看Cloudflare日志
wrangler tail
```

## 📋 常用命令

### 环境管理

```bash
docker-compose up -d              # 启动环境（后台运行）
docker-compose down               # 停止环境
docker-compose restart           # 重启环境
docker-compose ps                # 查看容器状态
docker-compose logs dev          # 查看容器日志
docker-compose logs -f dev       # 实时查看日志
```

### 容器操作

```bash
docker-compose exec dev bash     # 进入开发容器
docker-compose build --no-cache  # 重新构建镜像
docker system prune -f           # 清理Docker缓存
```

### 开发命令（在容器内执行）

```bash
# 进入容器后执行
pnpm dev                         # 启动Next.js开发服务器
pnpm build                       # 构建项目
pnpm cf:preview                  # Cloudflare预览部署
pnpm cf:deploy                   # Cloudflare生产部署
wrangler tail                    # 查看Cloudflare日志
wrangler whoami                  # 验证Cloudflare认证
node --version                   # 查看Node.js版本
wrangler --version               # 查看wrangler版本
```

## 🔧 故障排查

### 常见问题

#### 1. Docker未启动

```text
❌ Docker is not running. Please start Docker Desktop.
```

**解决方案**: 启动Docker Desktop

#### 2. API Token未配置

```text
⚠️ CLOUDFLARE_API_TOKEN is not set in .env.docker
```

**解决方案**: 按照上述步骤配置API Token

#### 2.5. Account ID找不到

**问题**: 无法在右侧边栏或API Tokens页面找到Account ID

**解决方案**: 使用地址栏方法（最可靠）

- Cloudflare界面经常更新，右侧边栏可能不显示Account ID
- API Tokens页面的界面布局可能变化
- **推荐方法**: 直接从浏览器地址栏复制

  ```text
  https://dash.cloudflare.com/[这里就是Account ID]/...
  ```

#### 3. 端口冲突

```text
Error: Port 3000 is already in use
```

**解决方案**:

- 停止占用端口的进程
- 或修改docker-compose.yml中的端口映射

#### 4. Node.js版本不兼容

```text
Wrangler requires at least Node.js v20.0.0. You are using v18.x.x
```

**解决方案**: 确保使用Node.js 20+（已在Dockerfile.dev中配置）

#### 5. wrangler认证失败

```text
Error: Not authenticated. Please run `wrangler auth login`
```

**解决方案**: 检查.env.docker中的API Token配置

### 查看详细日志

```bash
# 查看容器日志
docker-compose logs dev
docker-compose logs -f dev       # 实时日志

# 查看Docker Compose状态
docker-compose ps

# 进入容器调试
docker-compose exec dev bash
```

## 🎯 CMS重构工作流程

### 1. 启动开发环境

```bash
# 启动Docker容器
docker-compose up -d

# 进入开发容器
docker-compose exec dev bash
```

### 2. 验证环境

```bash
# 在容器内验证环境
node --version              # 应显示 v20.x.x
wrangler --version          # 应显示 4.19.1
wrangler whoami             # 验证Cloudflare认证
```

### 3. 开发和测试

```bash
# 在容器内进行开发
pnpm dev                    # 启动开发服务器（访问 http://localhost:3000）

# 在另一个终端窗口测试构建
docker-compose exec dev bash
pnpm build                  # 测试构建
```

### 4. Cloudflare部署测试

```bash
# 在容器内执行
pnpm cf:preview             # 预览部署（测试环境）
pnpm cf:deploy              # 生产部署
wrangler tail               # 查看实时日志
wrangler deployments list  # 查看部署历史
```

### 5. 停止环境

```bash
# 退出容器
exit

# 停止Docker环境
docker-compose down
```

## 📁 文件结构

```text
├── docker-compose.yml      # Docker编排配置
├── Dockerfile.dev          # 开发环境容器（Node.js 20 + wrangler）
├── .env.docker                    # Docker特定环境变量
└── docs/docker/
    ├── DOCKER_DEVELOPMENT_GUIDE.md  # 完整开发指南（本文档）
    └── DOCKER_QUICK_REFERENCE.md    # 快速参考
```

## 🔒 安全注意事项

1. **不要提交API Token**: .env.docker已在.gitignore中
2. **定期轮换Token**: 建议定期更新Cloudflare API Token
3. **最小权限原则**: API Token只授予必要的权限

## 💡 提示

- **首次启动**: 可能需要几分钟下载镜像和安装依赖
- **代码同步**: 宿主机代码修改会实时同步到容器内
- **认证持久化**: wrangler认证信息会保存在Docker volume中
- **环境要求**: 使用Node.js 20 + wrangler 4.19.1
- **端口映射**: 3000(Next.js) 和 8787(wrangler dev)
- **多终端**: 可以同时开启多个容器终端进行开发

## 🚀 成功验证清单

- ✅ Docker Desktop 正在运行
- ✅ API Token 和 Account ID 已配置
- ✅ 容器启动成功：`docker-compose ps`
- ✅ Node.js 版本正确：`node --version` (v20.x.x)
- ✅ wrangler 工作正常：`wrangler whoami`
- ✅ 开发服务器可访问：<http://localhost:3000>
