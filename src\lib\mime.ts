/**
 * MIME type utilities and helper functions
 * Leverages the 'mime' library to eliminate data duplication and reduce maintenance overhead
 */
import mime from 'mime';
import { FileTypeGroup } from '@/types/file';

/**
 * Web-safe MIME types that are widely supported across browsers
 * Used for validation and optimization decisions - kept minimal and focused
 */
// const WEB_SAFE_TYPES = new Set<string>([
//   // Images
//   'image/png', 'image/jpeg', 'image/gif', 'image/webp',
//   // Videos  
//   'video/mp4', 'video/webm',
//   // Audio
//   'audio/mpeg', 'audio/ogg', 'audio/wav',
//   // Documents
//   'application/pdf', 'text/plain'
// ]);

/**
 * Get MIME type from file extension using the mime library
 * Provides access to the full mime database without maintaining our own constants
 * @param extension File extension (with or without dot)
 * @returns MIME type string or fallback to octet-stream
 */
export function getMimeTypeFromExtension(extension: string): string {
  // Remove leading dot if present
  const cleanExtension = extension.startsWith('.') ? extension.slice(1) : extension;
  return mime.getType(cleanExtension) || 'application/octet-stream';
}

/**
 * Get file extension from MIME type using the mime library
 * @param mimeType MIME type string
 * @returns File extension without dot, or null if not found
 */
export function getExtensionFromMimeType(mimeType: string): string | null {
  return mime.getExtension(mimeType);
}

/**
 * Convert MIME type to project FileType enum
 * Maps MIME type categories to our internal file classification system
 * @param mimeType The MIME type string to categorize
 * @returns FileType enum value for consistent classification
 */
export function getFileTypeGroupFromMimeType(mimeType: string): FileTypeGroup {
  const [primaryType, subType] = mimeType.toLowerCase().split('/');
  
  switch (primaryType) {
    case 'image':
      return FileTypeGroup.IMAGE;
    case 'video':
      return FileTypeGroup.VIDEO;
    case 'audio':
      return FileTypeGroup.AUDIO;
    case 'text':
      // Handle specific text types
      if (subType === 'csv') {
        return FileTypeGroup.CSV;
      }
      return FileTypeGroup.TEXT;
    case 'application':
      return getApplicationFileType(subType || '');
    default:
      return FileTypeGroup.OTHER;
  }
}

/**
 * Helper function to categorize application/* MIME types
 * Provides more precise classification for the diverse application/* category
 * @param subType The subtype part of application/* MIME type
 * @returns Appropriate FileType for the application subtype
 */
function getApplicationFileType(subType: string): FileTypeGroup {
  // PDF documents
  if (subType === 'pdf') {
    return FileTypeGroup.PDF;
  }
  
  // Microsoft Word documents
  if (subType === 'msword' || 
      subType === 'vnd.openxmlformats-officedocument.wordprocessingml.document') {
    return FileTypeGroup.WORD;
  }
  
  // Microsoft Excel documents
  if (subType === 'vnd.ms-excel' || 
      subType === 'vnd.openxmlformats-officedocument.spreadsheetml.sheet') {
    return FileTypeGroup.EXCEL;
  }
  
  // Microsoft PowerPoint documents
  if (subType === 'vnd.ms-powerpoint' || 
      subType === 'vnd.openxmlformats-officedocument.presentationml.presentation') {
    return FileTypeGroup.PPT;
  }
  
  // CSV files (though typically text/csv, some systems use application/csv)
  if (subType === 'csv') {
    return FileTypeGroup.CSV;
  }
  
  // Archive types - compressed files and containers
  if (subType.includes('zip') || 
      subType.includes('rar') || 
      subType.includes('7z') ||
      subType.includes('tar') ||
      subType.includes('gzip') ||
      subType.includes('compress') ||
      subType.includes('x-bzip') ||
      subType.includes('x-xz') ||
      subType.includes('x-lzma') ||
      subType.includes('x-archive')) {
    return FileTypeGroup.ARCHIVE;
  }
  
  // Other document types that don't fit specific categories
  if (subType.includes('json') ||
      subType.includes('xml') ||
      subType.includes('yaml') ||
      subType.includes('rtf') ||
      subType.includes('oasis.opendocument')) {
    return FileTypeGroup.TEXT;
  }
  
  // Binary/unknown application types
  if (subType === 'octet-stream') {
    return FileTypeGroup.OTHER;
  }
  
  // Default for other application types
  return FileTypeGroup.OTHER;
}

/**
 * Extract file information from URL for processing and classification
 * Enhanced version using the mime library for accurate type detection
 * @param url The source file URL to analyze
 * @returns Object containing filename, MIME type, and file category
 */
export function extractFileInfoFromUrl(url: string): {
  fileName: string;
  mimeType: string;
  fileTypeGroup: FileTypeGroup;
} {
  try {
    const urlObj = new URL(url);
    const fileName = urlObj.pathname.split("/").pop() || 'unknown_file';
    
    // Use mime library for accurate MIME type detection
    const mimeType = getMimeTypeFromExtension(fileName);
    const fileTypeGroup = getFileTypeGroupFromMimeType(mimeType);
    
    return { fileName, mimeType, fileTypeGroup };
  } catch (error) {
    return {
      fileName: 'unknown_file',
      mimeType: 'application/octet-stream',
      fileTypeGroup: FileTypeGroup.OTHER,
    };
  }
}
