
function getAppName() {
  const appName = process.env.NEXT_PUBLIC_PROJECT_NAME; 
  if (!appName) {
    throw new Error('NEXT_PUBLIC_PROJECT_NAME is not set');
  }
  return appName;
}

function getAppId() {
  const appId = process.env.NEXT_PUBLIC_APP_ID;
  if (!appId) {
    throw new Error('NEXT_PUBLIC_APP_ID is not set');
  }
  return appId.trim();
}

export const app = {
  name: getAppName(),
  id: getAppId(),
}
