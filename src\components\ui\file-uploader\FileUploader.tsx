"use client";

import { useCallback } from "react";
import { useDropzone } from "react-dropzone";
import { useTranslations } from 'next-intl';
import { cn } from "@/lib/utils";
import { useFileUpload } from "@/hooks/useFileUpload";
import { UploadResult } from "@/types/file-upload";
import { UploadArea } from "./UploadArea";
import { UploadStatus } from "./UploadStatus";

interface FileUploaderProps {
  /** Accepted MIME types */
  acceptMimeList?: string[];
  /** Maximum number of files */
  maxFileCount?: number;
  /** Maximum file size in bytes */
  maxFileSize?: number;
  /** User UUID for authentication */
  userUuid?: string;
  /** Callback when upload completes successfully */
  onUploadComplete: (results: UploadResult[], files: File[]) => void;
  /** Callback when upload fails */
  onUploadError?: (error: string) => void;
  /** Whether the uploader is disabled */
  disabled?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Custom placeholder text */
  placeholder?: string;
  /** Display variant */
  variant?: 'default' | 'compact' | 'large';
}

/**
 * Complete file uploader component with drag and drop support
 * Handles the entire upload flow from file selection to completion
 */
export function FileUploader({
  acceptMimeList = ['image/jpeg', 'image/png', 'image/gif'],
  maxFileCount: maxFiles = 5,
  maxFileSize = 20 * 1024 * 1024, // 20MB default
  userUuid,
  onUploadComplete,
  onUploadError,
  disabled = false,
  className,
  placeholder,
  variant = 'default',
}: FileUploaderProps) {
  const t = useTranslations('common.file_uploader');
  const { 
    uploadFiles, 
    isUploading, 
    progress, 
    error, 
    results, 
    reset 
  } = useFileUpload();

  /**
   * Handle file drop/selection
   */
  const onDrop = useCallback(async (acceptedFiles: File[], rejectedFiles: any[]) => {
    if (disabled || acceptedFiles.length === 0) return;

    try {
      // Reset previous state
      reset();

      // Handle rejected files
      if (rejectedFiles.length > 0) {
        const rejectionReasons = rejectedFiles.map(({ errors }) => 
          errors.map((error: any) => error.message).join(', ')
        ).join('; ');
        
        const errorMessage = `${t('upload_failed', { fallback: '文件被拒绝' })}: ${rejectionReasons}`;
        onUploadError?.(errorMessage);
        return;
      }

      // Limit number of files
      const filesToUpload = acceptedFiles.slice(0, maxFiles);
      if (acceptedFiles.length > maxFiles) {
        console.warn(`Only uploading first ${maxFiles} files of ${acceptedFiles.length} selected`);
      }

      // Validate file sizes
      for (const file of filesToUpload) {
        if (file.size > maxFileSize) {
          const errorMessage = `文件 ${file.name} 大小超过限制 (${Math.round(maxFileSize / 1024 / 1024)}MB)`;
          onUploadError?.(errorMessage);
          return;
        }
      }

      // Validate MIME types if specified
      if (acceptMimeList.length > 0) {
        for (const file of filesToUpload) {
          if (!acceptMimeList.includes(file.type)) {
            const errorMessage = `文件 ${file.name} 格式不支持`;
            onUploadError?.(errorMessage);
            return;
          }
        }
      }

      console.log(`Starting upload of ${filesToUpload.length} files`, {
        files: filesToUpload.map(
          f => ({ name: f.name, size: f.size, type: f.type })
        )
      });

      const uploadResults = await uploadFiles(filesToUpload, userUuid);
      
      // Check if any uploads were successful
      const successfulUploads = uploadResults.filter(result => result.success);
      const failedUploads = uploadResults.filter(result => !result.success);

      if (successfulUploads.length > 0) {
        console.log(`Successfully uploaded ${successfulUploads.length} files`, {
          fileIds: successfulUploads.map(r => r.fileId)
        });
        const successfulFiles = filesToUpload.filter((_, index) => 
          uploadResults[index].success
        );
        onUploadComplete(successfulUploads, successfulFiles);
      }

      if (failedUploads.length > 0) {
        const errorMessage = `${failedUploads.length} 个文件${t('upload_failed', { fallback: '上传失败' })}: ${
          failedUploads.map(r => r.error).join(', ')
        }`;
        onUploadError?.(errorMessage);
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : t('upload_failed', { fallback: '上传失败' });
      console.error('Upload failed:', error);
      onUploadError?.(errorMessage);
    }
  }, [
    disabled,
    maxFiles,
    maxFileSize,
    acceptMimeList,
    uploadFiles,
    reset,
    onUploadComplete,
    onUploadError
  ]);

  // Configure dropzone
  const dropzoneConfig = {
    onDrop,
    accept: acceptMimeList.length > 0 ? acceptMimeList.reduce((acc, mime) => {
      acc[mime] = [];
      return acc;
    }, {} as Record<string, string[]>) : undefined,
    maxFiles,
    maxSize: maxFileSize,
    disabled: disabled || isUploading,
    multiple: maxFiles > 1,
  };

  const { getRootProps, getInputProps, isDragActive } = useDropzone(dropzoneConfig);

  // Determine if upload is complete
  const isComplete = results.length > 0 && !isUploading;
  const hasError = !!error;

  // Generate status message
  const getStatusMessage = () => {
    if (hasError) return error;
    if (isUploading) return t('uploading_files', { 
      count: results.length || maxFiles,
      fallback: `正在上传 ${results.length || maxFiles} 个文件...`
    });
    if (isComplete) {
      const successCount = results.filter(r => r.success).length;
      return t('uploaded_successfully', { 
        count: successCount,
        fallback: `成功上传 ${successCount} 个文件`
      });
    }
    return "";
  };

  return (
    <div className={cn("w-full space-y-4", className)}>
      {/* Upload area */}
      <UploadArea
        getRootProps={getRootProps}
        getInputProps={getInputProps}
        isDragActive={isDragActive}
        isUploading={isUploading}
        disabled={disabled}
        accept={acceptMimeList}
        maxFiles={maxFiles}
        variant={variant}
      />

      {/* Upload status - hide in compact and large modes */}
      {variant !== 'compact' && variant !== 'large' && (
        <UploadStatus
          isUploading={isUploading}
          progress={progress}
          message={getStatusMessage()}
          error={hasError ? error : null}
          isComplete={isComplete}
        />
      )}
    </div>
  );
}
