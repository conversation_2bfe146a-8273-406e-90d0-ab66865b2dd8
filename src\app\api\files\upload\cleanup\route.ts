import { NextRequest } from "next/server";
import { cleanupFailedUploads } from "@/services/file";
import { nextRespData, nextRespErr } from "@/lib/resp";
import logger from "@/lib/logger";
import {
  CleanupUploadRequest,
} from "@/types/file-upload";

/**
 * Clean up failed uploads and remove pending file records
 * Removes database records and attempts to delete files from cloud storage
 * 
 * POST /api/files/upload/cleanup
 * 
 * Request body:
 * {
 *   fileIds: ["file_uuid_1", "file_uuid_2", ...]
 * }
 * 
 * Response:
 * {
 *   code: 0,
 *   message: "Cleanup completed successfully",
 *   data: {
 *     message: "Cleanup completed",
 *     cleanedFileIds: ["file_uuid_1", "file_uuid_2"]
 *   }
 * }
 */
export async function POST(req: NextRequest) {
  try {
    // Extract user information from headers
    const userUuid = req.headers.get('x-user-uuid');
    if (!userUuid) {
      return nextRespErr("User authentication required");
    }

    // Parse and validate request body
    let requestBody: CleanupUploadRequest;
    let fileIds: string[];
    try {
      requestBody = await req.json();
      fileIds = requestBody.fileIds;
    } catch (error) {
      return nextRespErr("Invalid JSON in request body");
    }

    // Validate required fields
    if (!fileIds || !Array.isArray(fileIds) || fileIds.length === 0) {
      return nextRespErr("File IDs array is required and cannot be empty");
    }

    if (fileIds.length > 10) {
      return nextRespErr("Maximum 10 files per batch cleanup");
    }

    // Validate each file ID
    for (let i = 0; i < fileIds.length; i++) {
      const fileId = fileIds[i];
      
      if (!fileId || typeof fileId !== 'string') {
        return nextRespErr(`File ID ${i + 1}: must be a non-empty string`);
      }
    }

    logger.info('Processing upload cleanup request', {
      userUuid,
      fileIds,
      fileCount: fileIds.length
    }, { filePath: "app/api/files/upload/cleanup/route.ts", functionName: 'POST' });

    // Clean up failed uploads
    const result = await cleanupFailedUploads(fileIds);

    const response = {
      message: "Cleanup completed",
      cleanedFileIds: fileIds,
      result
    };

    logger.info('Successfully completed upload cleanup', {
      userUuid,
      cleanedCount: fileIds.length,
      cleanedFileIds: fileIds
    }, { filePath: "app/api/files/upload/cleanup/route.ts", functionName: 'POST' });

    return nextRespData(response);
  } catch (error) {
    logger.error('Failed to cleanup uploads', error, {
      userUuid: req.headers.get('x-user-uuid')
    }, { filePath: "app/api/files/upload/cleanup/route.ts", functionName: 'POST' });
    
    return nextRespErr("Failed to cleanup uploads");
  }
} 