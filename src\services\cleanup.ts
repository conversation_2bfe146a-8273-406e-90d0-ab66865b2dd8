/**
 * File cleanup service for maintenance tasks
 * Handles automated cleanup of expired files, orphaned files, and storage optimization
 * Essential for maintaining storage costs and data hygiene in production environments
 */
import { getExpiredFiles, updateFileStatus } from "@/models/file";
import { deleteFiles } from "@/lib/cloud-storage";
import { FileStatus } from "@/types/file";
import logger from "@/lib/logger";

/**
 * Result structure for cleanup operations
 * Provides detailed statistics about cleanup task execution
 */
interface CleanupResult {
  /** Total number of files processed during cleanup */
  processed: number;
  /** Number of files successfully cleaned up */
  successful: number;
  /** Number of files that failed cleanup */
  failed: number;
}

/**
 * Comprehensive cleanup service for file lifecycle management
 * Handles automated maintenance tasks including:
 * - Expired file removal from storage and database
 * - Orphaned file cleanup (files without valid references)
 * - Storage optimization and cost management
 * - Data integrity maintenance
 */
export class CleanupService {
  /**
   * Clean up expired files from both cloud storage and database
   * Removes files that have passed their expiration date to manage storage costs
   * Performs two-phase cleanup: storage deletion followed by database status update
   * @returns Promise resolving to CleanupResult with operation statistics
   * @throws Error if cleanup operation fails completely
   */
  async cleanupExpiredFiles(): Promise<CleanupResult> {
    try {
      const expiredFiles = await getExpiredFiles();
      
      logger.info('Starting expired files cleanup operation', 
        { expiredCount: expiredFiles.length }, 
        { filePath: "services/cleanup.ts", functionName: 'cleanupExpiredFiles' });
      
      let successful = 0;
      let failed = 0;
      
      for (const file of expiredFiles) {
        try {
          // First phase: Delete file from cloud storage to free up space
          const deleteResults = await deleteFiles([file.storage_key]); // Pass as array with single element
          const deletedFromStorage = deleteResults[0]; // Extract single result
          
          if (deletedFromStorage) {
            // Second phase: Update database status to mark as expired
            await updateFileStatus(file.file_id, FileStatus.EXPIRED);
            successful++;
            
            logger.info('Successfully cleaned up expired file', 
              { fileId: file.file_id, storageKey: file.storage_key }, 
              { filePath: "services/cleanup.ts", functionName: 'cleanupExpiredFiles' });
          } else {
            failed++;
            logger.warn('Failed to delete file from storage but marking as expired in database', 
              { fileId: file.file_id, storageKey: file.storage_key }, 
              { filePath: "services/cleanup.ts", functionName: 'cleanupExpiredFiles' });
            
            // Mark as expired in database even if storage deletion failed
            // This prevents the file from being served while allowing retry later
            await updateFileStatus(file.file_id, FileStatus.EXPIRED);
          }
        } catch (error) {
          failed++;
          logger.error('Failed to cleanup expired file', error, 
            { fileId: file.file_id }, 
            { filePath: "services/cleanup.ts", functionName: 'cleanupExpiredFiles' });
        }
      }
      
      const result: CleanupResult = {
        processed: expiredFiles.length,
        successful,
        failed
      };
      
      logger.info('Expired files cleanup operation completed', result, 
        { filePath: "services/cleanup.ts", functionName: 'cleanupExpiredFiles' });
      
      return result;
    } catch (error) {
      logger.error('Expired files cleanup operation failed', error, {}, 
        { filePath: "services/cleanup.ts", functionName: 'cleanupExpiredFiles' });
      throw error;
    }
  }
  
  /**
   * Clean up orphaned files (files without associated tasks or users)
   * Identifies and removes files that are no longer referenced by active entities
   * Helps maintain data integrity and reduce storage costs from abandoned files
   * @param maxAge Maximum age in hours for files to be considered orphaned (default: 24)
   * @returns Promise resolving to CleanupResult with operation statistics
   * @throws Error if cleanup operation fails
   */
  async cleanupOrphanFiles(maxAge: number = 24): Promise<CleanupResult> {
    try {
      // TODO: Implement orphan file detection logic
      // This would need a custom query to find files where:
      // - task_id references non-existent tasks
      // - user_uuid references non-existent users
      // - files are older than maxAge hours
      // - files are not referenced by any active entities
      
      logger.info('Orphan files cleanup requested but not yet implemented', 
        { maxAge }, 
        { filePath: "services/cleanup.ts", functionName: 'cleanupOrphanFiles' });
      
      return {
        processed: 0,
        successful: 0,
        failed: 0
      };
    } catch (error) {
      logger.error('Orphan files cleanup operation failed', error, { maxAge }, 
        { filePath: "services/cleanup.ts", functionName: 'cleanupOrphanFiles' });
      throw error;
    }
  }
  
  /**
   * Execute all available cleanup tasks in sequence
   * Comprehensive maintenance operation that runs multiple cleanup strategies
   * Suitable for scheduled execution (daily/weekly maintenance windows)
   * @returns Promise resolving to detailed results from all cleanup operations
   * @throws Error if any cleanup task fails critically
   */
  async runAllCleanupTasks(): Promise<{
    expiredFiles: CleanupResult;
    orphanFiles: CleanupResult;
  }> {
    try {
      logger.info('Starting comprehensive cleanup tasks execution', {}, 
        { filePath: "services/cleanup.ts", functionName: 'runAllCleanupTasks' });
      
      // Execute expired files cleanup
      const expiredFilesResult = await this.cleanupExpiredFiles();
      
      // Execute orphan files cleanup
      const orphanFilesResult = await this.cleanupOrphanFiles();
      
      const result = {
        expiredFiles: expiredFilesResult,
        orphanFiles: orphanFilesResult
      };
      
      logger.info('All cleanup tasks completed successfully', result, 
        { filePath: "services/cleanup.ts", functionName: 'runAllCleanupTasks' });
      
      return result;
    } catch (error) {
      logger.error('Cleanup tasks execution failed', error, {}, 
        { filePath: "services/cleanup.ts", functionName: 'runAllCleanupTasks' });
      throw error;
    }
  }
}

/**
 * Singleton instance of CleanupService for application-wide use
 * Provides consistent access to cleanup operations throughout the application
 * Can be used by scheduled jobs, admin interfaces, or maintenance scripts
 */
export const cleanupService = new CleanupService();