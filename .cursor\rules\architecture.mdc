# 架构设计规范

## 整体架构

本项目采用现代化的分层架构设计，确保代码的可维护性和可扩展性。

### 分层架构
- **表现层** - `src/components/`, `src/app/` (UI组件和页面)
- **业务层** - `src/services/` (业务逻辑处理)
- **数据层** - `src/models/` (数据库操作)
- **数据库层** - `src/db/` (Drizzle-ORM)

## 各层职责

### 1. 表现层 (Presentation Layer)
- **位置**: `src/components/`, `src/app/`
- **职责**: 
  - UI组件渲染
  - 用户交互处理
  - 路由和页面管理
  - 状态展示
- **规则**:
  - 只能调用服务层（Services）
  - 不直接访问数据库
  - 专注于UI逻辑

### 2. 业务逻辑层 (Business Layer)
- **位置**: `src/services/`
- **职责**:
  - 复杂业务逻辑处理
  - 跨模型的数据操作
  - 外部API集成
  - 任务编排和流程控制
- **规则**:
  - 可以调用数据层（Models）
  - 处理跨表的复杂查询
  - 包含业务验证逻辑

### 3. 数据访问层 (Data Layer)
- **位置**: `src/models/`
- **职责**:
  - 数据库CRUD操作
  - 数据验证
  - 简单的数据转换
  - 单表操作为主
- **规则**:
  - 直接使用Drizzle-ORM
  - 每个文件对应一个主要数据实体
  - 保持操作简单和原子性

### 4. 数据库层 (Database Layer)
- **位置**: `src/db/`
- **组成**:
  - `schema.ts` - 数据库表结构定义
  - `config.ts` - Drizzle配置
  - `index.ts` - 数据库连接
  - `migrations/` - 迁移文件

## 数据流向
用户请求 → Components → Services → Models → Database

## 关键设计原则

### 1. 单一职责原则
- 每一层只负责自己的核心职责
- 避免跨层级的直接调用

### 2. 依赖方向
- 上层依赖下层
- 下层不依赖上层
- 通过接口和类型实现解耦

### 3. 错误处理
- 每层负责自己的错误处理
- 向上层传递有意义的错误信息
- 使用统一的错误处理机制

### 4. 类型安全
- 端到端的TypeScript类型保护
- 数据库到前端的类型传递
- 使用Drizzle的类型推导能力

## AI集成架构
Components → AI Services → Task Models → AI Providers

### AI相关分层
- **AI Components** - AI相关UI组件
- **AI Services** - AI任务管理服务  
- **Task Models** - 任务数据操作
- **AI SDK** - AI提供商集成

## 最佳实践

1. **保持层次清晰** - 严格遵循分层调用关系
2. **接口优先** - 定义清晰的接口和类型
3. **兼容性设计** - 优先考虑跨平台兼容的架构方案
4. **性能平衡** - 在兼容性和性能之间寻找最佳平衡点

参考文件：[src/db/schema.ts](mdc:src/db/schema.ts)、[src/models/user.ts](mdc:src/models/user.ts)、[src/services/user.ts](mdc:src/services/user.ts)


- **Services** - 实现业务逻辑
- **Providers** - 管理全局状态和上下文
- **清晰分离** - 数据层、业务层、展示层职责明确

