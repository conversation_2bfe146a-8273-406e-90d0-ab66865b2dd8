/**
 * Replicate Model Provider
 * 
 * This module exports all Replicate-related model providers.
 * Main exports include:
 * - ReplicateImageGenerator: For generating images using various Replicate models
 */

import ImageGenerator from './image-generator';
import { Models } from './image-generator';

// export {
//   ReplicateImageGenerator,
//   ReplicateModels
// };

// Default export for convenience
export default {
  ImageGenerator: ImageGenerator,
  Models: Models
};

/*

这个地方（`export default {`）的作用是提供默认导出，使得模块可以以更简洁的方式被导入使用。

具体来说：

1. 默认导出允许其他模块通过简化的语法导入整个Replicate服务集合：
   ```typescript
   import Replicate from '@/aisdk/model-providers/replicate';
   
   // 可以直接这样使用
   const generator = new Replicate.ImageGenerator();
   ```

2. 相比命名导出，默认导出提供了更灵活的使用方式：
   ```typescript
   // 命名导出的使用方式
   import { ReplicateImageGenerator } from '@/aisdk/model-providers/replicate';
   const generator = new ReplicateImageGenerator();
   ```

3. 这种结构便于模块扩展，日后如果添加更多服务（如文本生成、音频处理等），可以直接在对象中添加新的属性：
   ```typescript
   export default {
     ImageGenerator: ReplicateImageGenerator,
     TextGenerator: ReplicateTextGenerator,
     // 更多服务...
   }
   ```

这是一种常见的模块化设计模式，让代码组织更清晰，调用更方便，也使项目更容易维护和扩展。

*/
