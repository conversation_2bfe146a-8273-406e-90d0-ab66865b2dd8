export function getIsoTimestr(): string {
  return new Date().toISOString();
}

export const getTimestamp = () => {
  let time = Date.parse(new Date().toUTCString());

  return time / 1000;
};

export const getMillisecond = () => {
  let time = new Date().getTime();

  return time;
};

export const getOneYearLaterTimestr = () => {
  const currentDate = new Date();
  const oneYearLater = new Date(currentDate);
  oneYearLater.setFullYear(currentDate.getFullYear() + 1);

  return oneYearLater.toISOString();
};

// ============================================================================

/**
 * Get timestamp in seconds
 * 更直接且性能可能稍好一些，因为它避免了字符串的转换和解析过程
 */
export const getTimestampSeconds = (): number => {
  return Math.floor(Date.now() / 1000);
};

/**
 * Common timezone constants for date formatting
 */
export const TimeZone = {
  CHINA: 'Asia/Shanghai',           // China Standard Time (UTC+8)
  US_EASTERN: 'America/New_York',   // US Eastern Time (UTC-5/-4)
  UK: 'Europe/London',              // UK Time (UTC+0/+1)
  UTC: 'UTC',                       // Coordinated Universal Time (UTC+0)
  JAPAN: 'Asia/Tokyo',              // Japan Standard Time (UTC+9)
  US_PACIFIC: 'America/Los_Angeles',// US Pacific Time (UTC-8/-7)
  GERMANY: 'Europe/Berlin',         // Central European Time (UTC+1/+2)
  SINGAPORE: 'Asia/Singapore',      // Singapore Time (UTC+8)
} as const;

/**
 * Type for timezone values
 */
export type TimeZoneValue = typeof TimeZone[keyof typeof TimeZone];

/**
 * Generate formatted date/time string with specified timezone
 * 
 * @param date - The Date object to format (defaults to current date)
 * @param timeZone - The timezone to use for formatting (defaults to UTC)
 * @param includeTime - Whether to include time portion (defaults to true)
 * @returns Formatted string in "YYYY-MM-DD" or "YYYY-MM-DD_HH-mm-ss" format
 * 
 * @example
 * // Generate current date-time in UTC
 * getFormattedDateTime() // "2025-01-24_22-30-25"
 * 
 * // Generate only date in UTC
 * getFormattedDateTime(new Date(), TimeZone.UTC, false) // "2025-01-24"
 * 
 * // Generate date-time in US Eastern timezone
 * getFormattedDateTime(new Date(), TimeZone.US_EASTERN) // "2025-01-24_09-30-25"
 */
export function getFormattedDateTime(
  date: Date = new Date(),
  timeZone: TimeZoneValue = TimeZone.UTC,
  includeTime: boolean = true
): string {
  // Use sv-SE locale for ISO-like formatting (YYYY-MM-DD format)
  const options: Intl.DateTimeFormatOptions = {
    timeZone: timeZone,
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    ...(includeTime && {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false // Use 24-hour format
    })
  };

  // Format the date using the specified options
  const formatted = date.toLocaleString('sv-SE', options);
  
  if (!includeTime) {
    // Return only date portion: "2025-01-24"
    return formatted;
  }
  
  // Convert "2025-01-24 22:30:25" to "2025-01-24_22-30-25" format
  // Replace space with underscore and colons with hyphens for file-safe naming
  return formatted.replace(' ', '_').replace(/:/g, '-');
}
