/**
 * Content Management Service Types
 * 
 * This module defines the core types and interfaces used throughout the content
 * management services. These types provide a contract that remains stable even
 * when the underlying data source changes (e.g., migrating from Contentlayer to
 * a Headless CMS).
 * 
 * FUTURE EXTENSIBILITY - Headless CMS Integration:
 * These types are designed to be CMS-agnostic, ensuring that components and
 * services can work with any backend (file-based, API-based, etc.) without
 * modification.
 */

/**
 * Content types supported by the content management system
 * 
 * - 'blog': Blog articles/posts
 * - 'product': Product pages/descriptions
 * - 'case-study': Case study documents
 * - 'other': Non-content pages (home, about, etc.)
 */
export type ContentType = 'blog' | 'product' | 'case-study' | 'other'

/**
 * Content page information extracted from pathname analysis
 * 
 * This interface represents the result of analyzing a URL to determine
 * what type of content is being viewed and its identifying information.
 */
export interface ContentPageInfo {
  /** The type of content being viewed */
  type: ContentType
  /** The unique identifier (slug) for the content, null for list pages */
  slug: string | null
  /** The current locale/language of the page */
  currentLocale: string
}

/**
 * Language version information for a specific piece of content
 * 
 * This interface represents the availability and access information
 * for a content item in a specific language.
 */
export interface LanguageVersion {
  /** The locale code (e.g., 'en', 'zh') */
  locale: string
  /** Whether the content exists in this language */
  exists: boolean
  /** The URL to access this language version */
  url: string
}

/**
 * Language switching strategy result
 * 
 * This interface represents the result of intelligent language switching,
 * including the target URL and the strategy used to determine it.
 */
export interface LanguageSwitchResult {
  /** The URL to navigate to */
  url: string
  /** The strategy used for switching */
  strategy: 'direct' | 'fallback-list'
  /** Optional reason for the chosen strategy */
  reason?: string
}
