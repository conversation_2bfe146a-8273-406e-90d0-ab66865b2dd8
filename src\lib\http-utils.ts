/**
 * HTTP response utilities for file downloads and content handling
 * 
 * This module provides HTTP-specific utilities that are commonly needed
 * for web applications, particularly for handling file downloads and
 * content delivery with proper browser compatibility.
 * 
 * Features:
 * - Content-Disposition header generation (RFC 6266 compliant)
 * - Unicode filename support for international users
 * - Proper escaping for HTTP headers
 * - Browser compatibility handling
 * - Cache control headers
 * - CORS headers
 */

import { sanitizeFileName } from './path';

// =============================================================================
// HTTP Header Utilities
// =============================================================================

/**
 * Content disposition types for HTTP responses
 */
export type ContentDisposition = 'attachment' | 'inline';

/**
 * Generates a complete Content-Disposition header value for HTTP responses
 * Implements RFC 6266 standard with full Unicode support and browser compatibility
 * 
 * ## Background & Problem Solved
 * International filenames in HTTP downloads present compatibility challenges:
 * - Legacy browsers only support ASCII characters in filenames
 * - Modern browsers support Unicode but require specific encoding (RFC 6266)
 * - Security risks from malicious filenames and HTTP header injection
 * - Need for graceful degradation across all browser versions
 * 
 * ## Implementation Strategy (Dual Approach)
 * This function uses a "progressive enhancement" approach:
 * 
 * 1. **ASCII-only files**: Simple format for universal compatibility
 *    Format: `attachment; filename="report.pdf"`
 * 
 * 2. **Unicode files**: Dual parameter approach per RFC 6266
 *    Format: `attachment; filename="fallback.pdf"; filename*=UTF-8''%encoded`
 *    - `filename`: ASCII fallback for legacy browsers (IE, old mobile browsers)
 *    - `filename*`: UTF-8 encoded original for modern browsers (Chrome, Firefox, Safari)
 * 
 * ## Browser Behavior
 * - **Modern browsers**: Read `filename*` parameter, display original Unicode filename
 * - **Legacy browsers**: Ignore unknown `filename*`, use ASCII `filename` fallback
 * - **Result**: All users get readable filenames, modern users get perfect experience
 * 
 * ## Security Features
 * - Strict filename sanitization prevents filesystem attacks
 * - HTTP header injection protection via proper escaping
 * - Path traversal attack prevention (../, ..\\ removal)
 * - Dangerous character removal (<>:"|?*) while preserving readability
 * 
 * @param filename - Original filename (supports full Unicode: 中文, 日本語, العربية, etc.)
 * @param disposition - Content disposition type:
 *   - 'attachment': Forces download dialog (default)
 *   - 'inline': Attempts browser preview (images, PDFs, etc.)
 * @returns RFC 6266 compliant Content-Disposition header value
 * 
 * @example
 * ```typescript
 * // Simple ASCII filename - universal compatibility
 * generateContentDisposition("report.pdf")
 * // => 'attachment; filename="report.pdf"'
 * 
 * // International filename - dual encoding for maximum compatibility
 * generateContentDisposition("用户报告.pdf")
 * // Modern browsers see: "用户报告.pdf"
 * // Legacy browsers see: "______.pdf" (fallback)
 * // => 'attachment; filename="______.pdf"; filename*=UTF-8\'\'%E7%94%A8%E6%88%B7%E6%8A%A5%E5%91%8A.pdf'
 * 
 * // Mixed language filename
 * generateContentDisposition("レポート-Report-报告.docx")
 * // => 'attachment; filename="________-report-___.docx"; filename*=UTF-8\'\'%E3%83%AC%E3%83%9D%E3%83%BC%E3%83%88-Report-%E6%8A%A5%E5%91%8A.docx'
 * 
 * // Inline disposition for browser preview
 * generateContentDisposition("product-image.jpg", "inline")
 * // => 'inline; filename="product-image.jpg"'
 * // Browser will try to display image instead of downloading
 * 
 * // Security: dangerous characters automatically cleaned
 * generateContentDisposition('My "危险<script>".pdf')
 * // => 'attachment; filename="my-_____.pdf"; filename*=UTF-8\'\'My%20%E5%8D%B1%E9%99%A9script.pdf'
 * // Quotes and script tags removed, Chinese characters preserved
 * 
 * // Real-world API usage
 * export async function downloadUserReport(userId: string) {
 *   const filename = `用户${userId}的报告.xlsx`;
 *   const buffer = await generateReport(userId);
 *   
 *   return new Response(buffer, {
 *     headers: {
 *       'Content-Disposition': generateContentDisposition(filename),
 *       'Content-Type': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
 *     }
 *   });
 * }
 * ```
 * 
 * @see https://tools.ietf.org/html/rfc6266 - RFC 6266: HTTP Content-Disposition Header Field
 * @see https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Content-Disposition
 */
export function generateContentDisposition(
  filename: string,
  disposition: ContentDisposition = 'attachment'
): string {
  // Step 1: Sanitize filename for HTTP headers using strict security level
  // - Removes dangerous characters: < > : " | ? * (filesystem threats)
  // - Preserves Unicode characters for international support
  // - Applies strict security suitable for HTTP headers
  const sanitizedFilename = sanitizeFileName(filename, {
    securityLevel: 'strict',     // Maximum security for HTTP context
    allowUnicode: true           // Keep international characters readable
  });

  // Step 2: Detect character encoding requirements
  // ASCII range: 0x00-0x7F (0-127 decimal)
  // Non-ASCII includes: Chinese (中文), Japanese (日本語), Arabic (العربية), 
  // Cyrillic (русский), emojis, and extended Latin characters
  const hasNonAscii = /[^\x00-\x7F]/.test(sanitizedFilename);

  if (!hasNonAscii) {
    // Step 3a: Simple case - ASCII-only filename
    // Use basic RFC 2616 format for universal browser compatibility
    // Escape internal quotes to prevent HTTP header parsing errors
    const escapedFilename = sanitizedFilename.replace(/"/g, '\\"');
    return `${disposition}; filename="${escapedFilename}"`;
  }

  // Step 3b: Complex case - Unicode filename requiring dual encoding strategy
  
  // Create ASCII fallback for legacy browsers (IE, old mobile browsers)
  // These browsers ignore the filename* parameter and use filename parameter
  // Convert Unicode to underscores for a readable fallback filename
  const asciiFallback = sanitizeFileName(sanitizedFilename, {
    securityLevel: 'strict',
    allowUnicode: false          // Convert Unicode → ASCII for compatibility
  });
  
  // Encode Unicode filename using percent-encoding (URL encoding)
  // This creates the filename* parameter value per RFC 6266
  // Example: "用户报告.pdf" → "%E7%94%A8%E6%88%B7%E6%8A%A5%E5%91%8A.pdf"
  const utf8Encoded = encodeURIComponent(sanitizedFilename);
  // encodeURIComponent 是 JavaScript 的内置全局函数，不需要导入就可以直接使用。
  // 对应的函数是 decodeURIComponent

  // Return dual-parameter format per RFC 6266:
  // - filename="fallback.pdf" → for legacy browsers
  // - filename*=UTF-8''encoded → for modern browsers
  // Modern browsers prioritize filename* and decode UTF-8 properly
  return `${disposition}; filename="${asciiFallback}"; filename*=UTF-8''${utf8Encoded}`;
}

/**
 * Generates cache control headers for static assets
 * Provides sensible defaults for different content types
 * 
 * @param maxAge - Cache duration in seconds
 * @param options - Additional cache options
 * @returns Cache control headers object
 * 
 * @example
 * ```typescript
 * // Long-term caching for static assets
 * generateCacheHeaders(31536000) // 1 year
 * // => { 'Cache-Control': 'public, max-age=31536000', 'Expires': '...' }
 * 
 * // No caching for dynamic content
 * generateCacheHeaders(0, { noCache: true })
 * // => { 'Cache-Control': 'no-cache, no-store, must-revalidate', 'Pragma': 'no-cache' }
 * ```
 */
export function generateCacheHeaders(
  maxAge: number,
  options: {
    noCache?: boolean;
    immutable?: boolean;
    private?: boolean;
  } = {}
): Record<string, string> {
  const { noCache = false, immutable = false, private: isPrivate = false } = options;

  if (noCache || maxAge === 0) {
    return {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    };
  }

  const visibility = isPrivate ? 'private' : 'public';
  const immutableFlag = immutable ? ', immutable' : '';
  
  return {
    'Cache-Control': `${visibility}, max-age=${maxAge}${immutableFlag}`,
    'Expires': new Date(Date.now() + maxAge * 1000).toUTCString()
  };
}

/**
 * Generates CORS headers for API responses
 * Provides secure defaults with configurable options
 * 
 * @param options - CORS configuration options
 * @returns CORS headers object
 * 
 * @example
 * ```typescript
 * // Basic CORS for API
 * generateCorsHeaders()
 * // => { 'Access-Control-Allow-Origin': '*', ... }
 * 
 * // Restricted CORS for sensitive endpoints
 * generateCorsHeaders({ 
 *   origin: 'https://myapp.com',
 *   credentials: true 
 * })
 * ```
 */
export function generateCorsHeaders(options: {
  origin?: string | string[];
  methods?: string[];
  headers?: string[];
  credentials?: boolean;
  maxAge?: number;
} = {}): Record<string, string> {
  const {
    origin = '*',
    methods = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    headers = ['Content-Type', 'Authorization'],
    credentials = false,
    maxAge = 86400 // 24 hours
  } = options;

  const corsHeaders: Record<string, string> = {
    'Access-Control-Allow-Origin': Array.isArray(origin) ? origin.join(', ') : origin,
    'Access-Control-Allow-Methods': methods.join(', '),
    'Access-Control-Allow-Headers': headers.join(', '),
    'Access-Control-Max-Age': maxAge.toString()
  };

  if (credentials) {
    corsHeaders['Access-Control-Allow-Credentials'] = 'true';
  }

  return corsHeaders;
} 