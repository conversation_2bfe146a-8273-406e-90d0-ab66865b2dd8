"use client";

import { DropzoneRootProps, DropzoneInputProps } from "react-dropzone";
import { useTranslations } from 'next-intl';
import { cn } from "@/lib/utils";
import { Upload, FileIcon, ImageIcon, Plus } from "lucide-react";

interface UploadAreaProps {
  getRootProps: () => DropzoneRootProps;
  getInputProps: () => DropzoneInputProps;
  isDragActive: boolean;
  isUploading: boolean;
  disabled: boolean;
  accept?: string[];
  maxFiles?: number;
  variant?: 'default' | 'compact' | 'large';
  className?: string;
}

/**
 * Upload area component with drag and drop support
 * Provides visual feedback for drag states and upload status
 */
export function UploadArea({
  getRootProps,
  getInputProps,
  isDragActive,
  isUploading,
  disabled,
  accept = [],
  maxFiles = 5,
  variant = 'default',
  className,
}: UploadAreaProps) {
  const t = useTranslations('common.file_uploader');
  
  // Determine the primary file type for better UI
  const primaryType = accept.length > 0 ? accept[0].split('/')[0] : 'file';
  
  // Get appropriate icon based on file type
  const getIcon = () => {
    if (primaryType === 'image') {
      return <ImageIcon className="h-6 w-6 text-muted-foreground" />;
    }
    return <FileIcon className="h-6 w-6 text-muted-foreground" />;
  };

  // Get appropriate text based on file type
  const getMainText = () => {
    if (isDragActive) {
      return t('drop_active', { fallback: '释放文件开始上传' });
    }
    
    if (isUploading) {
      return t('uploading', { fallback: '正在上传...' });
    }

    const fileTypeKey = `file_types.${primaryType}`;
    const fileTypeText = t(fileTypeKey, { fallback: primaryType === 'image' ? '图片' : 
                                                    primaryType === 'video' ? '视频' : 
                                                    primaryType === 'audio' ? '音频' : '文件' });
    
    return t('click_or_drag', { 
      fileType: fileTypeText,
      fallback: `点击或拖拽${fileTypeText}到此处上传` 
    });
  };

  const getSubText = () => {
    if (isDragActive || isUploading) {
      return "";
    }

    const typeTexts = [];
    if (accept.includes('image/jpeg') || accept.includes('image/png')) {
      typeTexts.push('JPG, PNG');
    }
    if (accept.includes('image/gif')) {
      typeTexts.push('GIF');
    }
    if (accept.includes('image/webp')) {
      typeTexts.push('WEBP');
    }
    if (accept.includes('video/mp4')) {
      typeTexts.push('MP4');
    }
    
    const supportedText = typeTexts.length > 0 ? 
      t('supported_formats', { 
        formats: typeTexts.join(', '),
        fallback: `支持 ${typeTexts.join(', ')}`
      }) : '';
    
    const maxText = maxFiles > 1 ? 
      t('max_files_multiple', { 
        count: maxFiles,
        fallback: `最多 ${maxFiles} 张图片` 
      }) : 
      t('max_files_single', { fallback: '单张图片' });
    
    return supportedText ? `${supportedText}，${maxText}` : maxText;
  };

  // Compact variant for grid integration
  if (variant === 'compact') {
    return (
      <div
        {...getRootProps()}
        className={cn(
          "h-full w-full border-2 border-dashed rounded flex flex-col items-center justify-center transition-colors cursor-pointer aspect-square",
          isDragActive 
            ? "border-primary bg-primary/10" 
            : "border-muted-foreground/25 hover:border-primary/50",
          isUploading && "pointer-events-none opacity-50",
          disabled && "pointer-events-none opacity-40",
          "bg-muted/30 hover:bg-muted/50"
        )}
      >
        <input {...getInputProps()} />
        
        {isUploading ? (
          <div className="w-5 h-5 border-2 border-primary border-t-transparent rounded-full animate-spin" />
        ) : (
          <Plus className="h-8 w-8 text-muted-foreground" />
        )}
      </div>
    );
  }

  // Large variant - like default but without progress indicator
  if (variant === 'large') {
    return (
      <div
        {...getRootProps()}
        className={cn(
          "relative flex flex-col items-center justify-center rounded-lg border-2 border-dashed transition-all duration-200 cursor-pointer h-full p-1",
          {
            // Normal state
            "border-border bg-background hover:bg-accent hover:border-accent-foreground/50": 
              !isDragActive && !isUploading && !disabled,
            
            // Drag active state
            "border-primary bg-primary/5 border-solid": 
              isDragActive && !disabled,
            
            // Uploading state
            "border-blue-300 bg-blue-50 dark:bg-blue-950/20": 
              isUploading,
            
            // Disabled state
            "border-muted bg-muted cursor-not-allowed opacity-60": 
              disabled,
          },
          className
        )}
      >
        <input {...getInputProps()} />
        
        {/* Upload icon with animation */}
        <div className={cn(
          "mb-2 transition-transform duration-200",
          {
            "scale-110": isDragActive,
            "animate-pulse": isUploading,
          }
        )}>
          {isUploading ? (
            <div className="relative">
              {getIcon()}
              <Upload className="h-3 w-3 text-primary absolute -top-1 -right-1 animate-bounce" />
            </div>
          ) : (
            getIcon()
          )}
        </div>

        {/* Main text */}
        <p className={cn(
          "text-xs font-medium mb-1 text-center transition-colors duration-200",
          {
            "text-primary": isDragActive,
            "text-blue-600 dark:text-blue-400": isUploading,
            "text-foreground": !isDragActive && !isUploading && !disabled,
            "text-muted-foreground": disabled,
          }
        )}>
          {getMainText()}
        </p>

        {/* Sub text */}
        {getSubText() && (
          <p className="text-xs text-muted-foreground text-center">
            {getSubText()}
          </p>
        )}
        
        {/* No progress indicator for large variant */}
      </div>
    );
  }

  // Default variant - existing code
  return (
    <div
      {...getRootProps()}
      className={cn(
        "relative flex flex-col items-center justify-center rounded-lg border-2 border-dashed transition-all duration-200 cursor-pointer h-full p-1",
        {
          // Normal state
          "border-border bg-background hover:bg-accent hover:border-accent-foreground/50": 
            !isDragActive && !isUploading && !disabled,
          
          // Drag active state
          "border-primary bg-primary/5 border-solid": 
            isDragActive && !disabled,
          
          // Uploading state
          "border-blue-300 bg-blue-50 dark:bg-blue-950/20 cursor-wait": 
            isUploading,
          
          // Disabled state
          "border-muted bg-muted cursor-not-allowed opacity-60": 
            disabled,
        },
        className
      )}
    >
      <input {...getInputProps()} />
      
      {/* Upload icon with animation */}
      <div className={cn(
        "mb-2 transition-transform duration-200",
        {
          "scale-110": isDragActive,
          "animate-pulse": isUploading,
        }
      )}>
        {isUploading ? (
          <div className="relative">
            {getIcon()}
            <Upload className="h-3 w-3 text-primary absolute -top-1 -right-1 animate-bounce" />
          </div>
        ) : (
          getIcon()
        )}
      </div>

      {/* Main text */}
      <p className={cn(
        "text-xs font-medium mb-1 text-center transition-colors duration-200",
        {
          "text-primary": isDragActive,
          "text-blue-600 dark:text-blue-400": isUploading,
          "text-foreground": !isDragActive && !isUploading && !disabled,
          "text-muted-foreground": disabled,
        }
      )}>
        {getMainText()}
      </p>

      {/* Sub text */}
      {getSubText() && (
        <p className="text-xs text-muted-foreground text-center">
          {getSubText()}
        </p>
      )}

      {/* Upload progress indicator */}
      {isUploading && (
        <div className="absolute bottom-0 left-0 right-0 h-1 bg-muted rounded-b-lg overflow-hidden">
          <div className="h-full bg-primary animate-pulse" />
        </div>
      )}
    </div>
  );
} 