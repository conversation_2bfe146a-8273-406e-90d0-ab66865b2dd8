import dynamic from "next/dynamic";
import "./markdown.css";

// 动态导入 MDEditor.Markdown 组件
const MDMarkdown = dynamic(
  () => import("@uiw/react-md-editor").then((mod) => ({ default: mod.default.Markdown })),
  { ssr: false }
);

export default function Markdown({ content }: { content: string }) {
  return (
    <MDMarkdown
      className="markdown bg-background"
      source={content}
      components={{
        a: ({ children, ...props }: any) => (
          <a {...props} target="_blank" rel="noopener noreferrer">
            {children}
          </a>
        ),
      }}
    />
  );
}
