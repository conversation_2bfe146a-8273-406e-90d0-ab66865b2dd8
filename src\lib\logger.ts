/**
 * Logger utility for structured logging with correlation ID.
 *
 * This module provides a standardized way to log information in a structured JSON format,
 * making it easier to parse and analyze logs in production environments.
 * All logs include a timestamp and appropriate log level.
 * 
 * Each log can include a logLocation object that tracks the source file and function
 * where the log was generated, making debugging easier in large applications.
 * 
 * Configuration is controlled by:
 * - Environment variables (LOGGER_ENABLED, LOGGER_LEVEL)
 * - Module constants (as fallbacks)
 */

/**
 * Available log levels
 */
enum LogLevel {
  INFO = 'INFO',
  WARN = 'WARN',
  ERROR = 'ERROR',
}

/**
 * Extended context type that combines required fields with optional additional data
 */
// export type ExtendedLogContext = logLocation & Record<string, any>;

/**
 * Default configuration when environment variables are not set
 */
const DEFAULT_CONFIG = {
  enabled: true,
  levels: [LogLevel.INFO, LogLevel.WARN, LogLevel.ERROR],
};

/**
 * ANSI color codes for console output
 */
const COLORS = {
  reset: '\x1b[0m',
  info: '\x1b[36m', // <PERSON>an
  warn: '\x1b[33m', // Yellow
  error: '\x1b[31m', // Red
};

/**
 * Determines if logging is enabled based on environment variables or defaults
 * @returns {boolean} Whether logging is enabled
 */
const isLoggingEnabled = (): boolean => {
  const envValue = process.env.LOGGER_ENABLED;
  if (envValue !== undefined) {
    return envValue.toLowerCase() === 'true';
  }
  return DEFAULT_CONFIG.enabled;
};

/**
 * Gets enabled log levels based on environment variables or defaults
 * @returns {LogLevel[]} Array of enabled log levels
 */
const getEnabledLogLevels = (): LogLevel[] => {
  const envValue = process.env.LOGGER_LEVEL;
  if (envValue) {
    const levels = envValue.split(',')
      .map(level => level.trim().toUpperCase());
    
    const validLevels = levels.filter(level => Object.values(LogLevel).includes(level as LogLevel)) as LogLevel[];

    const invalidLevels = levels.filter(level => !Object.values(LogLevel).includes(level as LogLevel));
    if (invalidLevels.length > 0) {
      console.warn(`[Logger] Ignored invalid log levels: ${invalidLevels.join(', ')}`);
    }

    return validLevels;
  }
  return DEFAULT_CONFIG.levels;
};

/**
 * Checks if a specific log level is enabled
 * @param {LogLevel} level - The log level to check
 * @returns {boolean} Whether the log level is enabled
 */
const isLevelEnabled = (level: LogLevel): boolean => {
  if (!isLoggingEnabled()) return false;
  return getEnabledLogLevels().includes(level);
};

/**
 * Required context information for structured logging
 * Ensures all log entries contain necessary source identification
 */
export interface logLocation {
  /** File path of the caller (relative to project root) */
  filePath: string;
  /** Name of the function/method where the log is called from */
  functionName: string;
}

/**
 * Logger object containing methods for different log levels
 */
const logger = {
  /**
   * Logs an informational message
   * 
   * @param {string} message - The main log message
   * @param {Record<string, any>} [context] - Optional additional contextual data
   * @param {logLocation} [logLocation] - Optional information about the log source location
   */
  info: (message: string, 
        context?: Record<string, any>, 
        logLocation?: logLocation) => {
    if (!isLevelEnabled(LogLevel.INFO)) return;
    
    const logData = JSON.stringify({
      level: LogLevel.INFO,
      timestamp: new Date().toISOString(),
      message,
      ...(context && { context }),
      ...(logLocation && { logLocation }),
    }, null, 2);
    
    console.log(`${COLORS.info}${logData}${COLORS.reset}`);
  },

  /**
   * Logs a warning message
   * 
   * @param {string} message - The warning message
   * @param {Record<string, any>} [context] - Optional additional contextual data
   * @param {logLocation} [logLocation] - Optional information about the log source location
   */
  warn: (message: string, 
        context?: Record<string, any>, 
        logLocation?: logLocation) => {
    if (!isLevelEnabled(LogLevel.WARN)) return;
    
    const logData = JSON.stringify({
      level: LogLevel.WARN,
      timestamp: new Date().toISOString(),
      message,
      ...(context && { context }),
      ...(logLocation && { logLocation }),
    }, null, 2);
    
    console.warn(`${COLORS.warn}${logData}${COLORS.reset}`);
  },

  /**
   * Logs an error message with optional error object details
   * 
   * @param {string} message - The error message description
   * @param {Error | unknown} [error] - Optional error object that will be deconstructed for logging
   * @param {Record<string, any>} [context] - Optional additional contextual data
   * @param {logLocation} [logLocation] - Optional information about the log source location
   */
  error: (message: string, 
          error?: Error | unknown, 
          context?: Record<string, any>,
          logLocation?: logLocation) => {
    if (!isLevelEnabled(LogLevel.ERROR)) return;
    
    const errorInfo =
      error instanceof Error
        ? {
            errorName: error.name,
            errorMessage: error.message,
            stack: error.stack,
          }
        : { errorDetail: error };
    
    const logData = JSON.stringify({
      level: LogLevel.ERROR,
      timestamp: new Date().toISOString(),
      message,
      ...(errorInfo && { errorInfo }),
      ...(context && { context }),
      ...(logLocation && { logLocation }),
    }, null, 2);
    
    console.error(`${COLORS.error}${logData}${COLORS.reset}`);
  },
};

export default logger;
