---
title: "ShipAny 快速入门：几小时内构建你的 AI SaaS"
slug: "getting-started-with-shipany"
description: "学习如何使用 ShipAny 强大的模板和组件快速构建和部署你的 AI SaaS 应用程序。"
coverImage: "https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=400&fit=crop"
author: "ShipAny 团队"
authorImage: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100&h=100&fit=crop&crop=face"
publishedAt: "2025-01-17"
featured: true
tags: ["教程", "快速入门", "ai-saas"]
---

# ShipAny 快速入门

欢迎使用 ShipAny！这份综合指南将带你使用我们强大的模板系统构建你的第一个 AI SaaS 应用程序。

## 什么是 ShipAny？

ShipAny 是一个专为快速高效构建 AI SaaS 创业项目而设计的 Next.js 样板。通过预构建的组件、身份验证、支付处理和 AI 集成，你可以专注于你的独特价值主张，而不是样板代码。

## 核心特性

- **🚀 快速开发**：预构建的组件和模板
- **🤖 AI 集成**：开箱即用的 AI SDK 集成
- **💳 支付处理**：内置 Stripe 集成
- **🌍 国际化**：多语言支持
- **📱 响应式设计**：移动优先的方法
- **🔐 身份验证**：安全的用户管理

## 快速开始

### 1. 克隆仓库

```bash
git clone https://github.com/shipany/shipany-template
cd shipany-template
```

### 2. 安装依赖

```bash
pnpm install
```

### 3. 设置环境变量

创建一个 `.env.local` 文件并配置：

```env
NEXT_PUBLIC_WEB_URL=http://localhost:3000
DATABASE_URL=your_database_url
NEXTAUTH_SECRET=your_secret
STRIPE_SECRET_KEY=your_stripe_key
```

### 4. 运行开发服务器

```bash
pnpm dev
```

## 构建你的第一个功能

让我们创建一个简单的 AI 驱动的文本生成器：

### 1. 创建 API 路由

```typescript
// app/api/generate/route.ts
import { openai } from '@ai-sdk/openai'
import { generateText } from 'ai'

export async function POST(request: Request) {
  const { prompt } = await request.json()
  
  const { text } = await generateText({
    model: openai('gpt-3.5-turbo'),
    prompt: `基于以下内容生成创意文本：${prompt}`,
  })
  
  return Response.json({ text })
}
```

### 2. 创建前端组件

```tsx
'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'

export function TextGenerator() {
  const [prompt, setPrompt] = useState('')
  const [result, setResult] = useState('')
  const [loading, setLoading] = useState(false)

  const handleGenerate = async () => {
    setLoading(true)
    try {
      const response = await fetch('/api/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ prompt }),
      })
      const data = await response.json()
      setResult(data.text)
    } catch (error) {
      console.error('错误:', error)
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="space-y-4">
      <Textarea
        placeholder="输入你的提示..."
        value={prompt}
        onChange={(e) => setPrompt(e.target.value)}
      />
      <Button onClick={handleGenerate} disabled={loading}>
        {loading ? '生成中...' : '生成'}
      </Button>
      {result && (
        <div className="p-4 bg-muted rounded-lg">
          {result}
        </div>
      )}
    </div>
  )
}
```

## 下一步

现在你已经设置好了基础，你可以：

1. **自定义 UI**：修改组件以匹配你的品牌
2. **添加更多 AI 功能**：集成额外的 AI 模型
3. **设置支付**：为订阅配置 Stripe
4. **部署**：部署到 Vercel 或你首选的平台

## 结论

ShipAny 提供了快速构建和启动 AI SaaS 所需的一切。凭借其全面的功能集和开发者友好的架构，你可以专注于最重要的事情：为用户构建优秀的产品。

准备好发布你的下一个 AI SaaS 了吗？今天就开始使用 ShipAny 吧！
