/**
 * Content Detection Service
 * 
 * This service handles URL analysis and content type detection. It parses
 * pathnames to determine what type of content is being viewed and extracts
 * relevant information like content type and slug.
 * 
 * Key Features:
 * - URL pattern matching for different content types
 * - Locale-aware path parsing
 * - Support for both content detail pages and list pages
 * 
 * FUTURE EXTENSIBILITY:
 * This service is designed to work with any URL structure and can be easily
 * extended to support new content types or URL patterns without affecting
 * other parts of the system.
 */

import type { ContentType, ContentPageInfo } from './types'

/**
 * Detect if the current pathname is a content page and extract information
 * 
 * This function analyzes the current URL pathname to determine what type of content
 * is being viewed and extracts relevant information like content type and slug.
 * It handles both localized and non-localized URLs.
 * 
 * URL Pattern Examples:
 * - '/blogs/my-post' -> { type: 'blog', slug: 'my-post', currentLocale: 'en' }
 * - '/zh/products/my-product' -> { type: 'product', slug: 'my-product', currentLocale: 'zh' }
 * - '/case-studies/' -> { type: 'case-study', slug: null, currentLocale: 'en' }
 * - '/about' -> { type: 'other', slug: null, currentLocale: 'en' }
 * 
 * @param pathname - Current pathname from Next.js router
 * @param currentLocale - Current locale from URL parameters
 * @returns Content page information object
 */
export function detectContentPage(pathname: string, currentLocale: string): ContentPageInfo {
  // Remove locale prefix from pathname for consistent analysis
  // This normalizes URLs like '/zh/blogs/post' to '/blogs/post'
  let cleanPath = pathname
  if (currentLocale !== 'en' && pathname.startsWith(`/${currentLocale}`)) {
    cleanPath = pathname.replace(`/${currentLocale}`, '')
  }

  // Detect blog content pages
  // Matches URLs like '/blogs/my-post' or '/blogs/'
  if (cleanPath.startsWith('/blogs/')) {
    const slug = cleanPath.replace('/blogs/', '')
    return {
      type: 'blog',
      slug: slug || null,  // null for list pages like '/blogs/'
      currentLocale
    }
  }

  // Detect product content pages
  // Matches URLs like '/products/my-product' or '/products/'
  if (cleanPath.startsWith('/products/')) {
    const slug = cleanPath.replace('/products/', '')
    return {
      type: 'product',
      slug: slug || null,  // null for list pages like '/products/'
      currentLocale
    }
  }

  // Detect case study content pages
  // Matches URLs like '/case-studies/my-case-study' or '/case-studies/'
  if (cleanPath.startsWith('/case-studies/')) {
    const slug = cleanPath.replace('/case-studies/', '')
    return {
      type: 'case-study',
      slug: slug || null,  // null for list pages like '/case-studies/'
      currentLocale
    }
  }

  // Default case for non-content pages (home, about, contact, etc.)
  return {
    type: 'other',
    slug: null,
    currentLocale
  }
}

/**
 * Get base path for content type
 * 
 * This helper function maps content types to their corresponding URL base paths.
 * It centralizes the URL structure configuration for easy maintenance.
 * 
 * @param contentType - Type of content
 * @returns Base path string for the content type
 */
export function getContentBasePath(contentType: ContentType): string {
  switch (contentType) {
    case 'blog':
      return '/blogs'
    case 'product':
      return '/products'
    case 'case-study':
      return '/case-studies'
    default:
      return ''
  }
}
