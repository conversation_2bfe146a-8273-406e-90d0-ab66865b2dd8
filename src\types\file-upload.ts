/**
 * File upload related type definitions for presigned URL upload flow
 * Supports both single and batch file uploads with unified API design
 */

import { FileInfo, FileCategory } from './file';

/**
 * Information about a file provided by the client for upload.
 */
export interface FileUploadInfo {
  fileName: string;
  mimeType: string;
  fileSize: number;
}

/**
 * Request structure for the presigned upload API endpoint.
 */
export interface PresignedUploadRequest {
  /** An array of file metadata objects. */
  filesInfo: FileUploadInfo[];
  /** The business category for the upload, which determines storage path and access rules. */
  fileCategory: FileCategory;
}

/**
 * Contains all the necessary data for a client to perform a direct-to-storage upload.
 */
export interface PresignedUploadInfo {
  /** The unique ID assigned to this file record. */
  fileId: string;
  /** The presigned URL for the PUT request to cloud storage. */
  presignedUrl: string;
  /** Required form fields for POST requests (if ever used, currently empty for PUT). */
  fields: Record<string, string>;
  /** ISO string indicating when the presigned URL expires. */
  expiresAt: string;
}

/**
 * Response structure for the presigned upload API endpoint.
 */
export interface PresignedUploadResponse {
  presignedUploadInfos: PresignedUploadInfo[];
}

export interface UploadResult {
  fileId: string;
  success: boolean;
  fileName: string;
  fileSize: number;
  mimeType: string;
  error?: string;
}

/**
 * Request structure for confirming that uploads have completed.
 */
export interface ConfirmUploadRequest {
  /** An array of file IDs that were successfully uploaded. */
  fileIds: string[];
}

/**
 * Response structure for the upload confirmation endpoint.
 */
export interface ConfirmUploadResponse {
  /** An array of the confirmed file records with their direct URLs. */
  confirmedFiles: FileInfo[];
}

/**
 * Request structure for cleaning up failed uploads.
 */
export interface CleanupUploadRequest {
  /** Array of file IDs to clean up. */
  fileIds: string[];
}

/**
 * Represents a file that has been selected on the client-side, extended
 * with a local preview URL for immediate UI feedback before upload.
 */
export interface UploadedFile extends FileInfo {
  /** A local object URL (e.g., from `URL.createObjectURL`) for client-side preview. */
  previewUrl: string;
}
