{"extends": ["next/core-web-vitals", "next/typescript"], "rules": {"react-hooks/rules-of-hooks": "warn", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": "warn", "react/display-name": "warn", "@next/next/no-html-link-for-pages": "warn", "prefer-const": "warn", "import/no-anonymous-default-export": "warn", "react-hooks/exhaustive-deps": "warn", "jsx-a11y/alt-text": "warn", "@next/next/no-img-element": "warn"}}