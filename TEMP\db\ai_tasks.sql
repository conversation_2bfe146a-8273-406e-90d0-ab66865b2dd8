-- AI Tasks Management
CREATE TABLE ai_tasks (
    id SERIAL PRIMARY KEY,
    task_id VARCHAR(255) UNIQUE NOT NULL,
    user_uuid VARCHAR(255) NOT NULL,
    task_type VARCHAR(50) NOT NULL, -- 'image' or future 'video'
    status VARCHAR(50) NOT NULL, -- 'pending', 'processing', 'completed', 'failed'
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    completed_at TIMESTAMPTZ,
    params JSONB NOT NULL, -- Store task parameters
    results JSONB, -- Store results (URLs, etc.)
    error TEXT, -- Store error information
    model_id VARCHAR(100) NOT NULL, -- gpt4o, etc.
    provider VARCHAR(50) NOT NULL, -- 'kie', etc.
    provider_task_id VARCHAR(255), -- Task ID from AI provider
    progress INT NOT NULL DEFAULT 0 -- Progress 0-100
);

-- Indexes for efficient querying
CREATE INDEX idx_ai_tasks_user_uuid ON ai_tasks(user_uuid);
CREATE INDEX idx_ai_tasks_status ON ai_tasks(status);
CREATE INDEX idx_ai_tasks_created_at ON ai_tasks(created_at);
CREATE INDEX idx_ai_tasks_model_id ON ai_tasks(model_id);

