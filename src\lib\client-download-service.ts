/**
 * Client-side download utilities
 * Extracted from DownloadService for browser use (without server-only dependencies)
 */

export interface ClientDownloadOptions {
  url: string;
  customFilename: string;
  onProgress?: ProgressCallback;
  strategy?: ClientDownloadStrategy;
}

export interface DownloadResult {
  success: boolean;
  method: string;
  size: number;
  customFilename: string;
  error?: string;
}

export type ClientDownloadStrategy = 'blob' | 'direct';
export type ProgressCallback = (progress: number) => void;

/**
 * Client-side download service 
 * Contains the download logic from DownloadService but without server dependencies
 */
export class ClientDownloadService {
  /**
   * Download file using optimal client-side strategy
   */
  async downloadFile(options: ClientDownloadOptions): Promise<DownloadResult> {
    const { url, customFilename, onProgress, strategy = 'blob' } = options;
    
    try {
      switch (strategy) {
        case 'blob':
          return await this.downloadViaBlob(url, customFilename, onProgress);
        case 'direct':
          return await this.downloadDirect(url, customFilename);
        default:
          throw new Error(`Unknown download strategy: ${strategy}`);
      }
    } catch (error) {
      console.error('Download failed, falling back to direct method:', error);
      return await this.downloadDirect(url, customFilename);
    }
  }

  /**
   * Download file using fetch and blob creation
   * Works with any URL and provides progress tracking
   * (Extracted from DownloadService.downloadViaBlob)
   */
  private async downloadViaBlob(
    url: string, 
    customFilename: string, 
    onProgress?: ProgressCallback
  ): Promise<DownloadResult> {
    const response = await fetch(url);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const total = parseInt(response.headers.get('content-length') || '0');
    let loaded = 0;

    // Read response stream with progress tracking
    const reader = response.body?.getReader();
    const chunks: Uint8Array[] = [];

    if (reader) {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        chunks.push(value);
        loaded += value.length;
        
        // Report progress if callback provided and total size known
        if (onProgress && total > 0) {
          onProgress((loaded / total) * 100);
        }
      }
    }

    // Create blob and trigger download
    const blob = new Blob(chunks);
    const blobUrl = URL.createObjectURL(blob);
    
    this.triggerDownload(blobUrl, customFilename);
    URL.revokeObjectURL(blobUrl);
    
    return { 
      success: true, 
      method: 'blob', 
      size: loaded,
      customFilename 
    };
  }

  /**
   * Direct download fallback - opens file in new tab
   * (Extracted from DownloadService.downloadDirect)
   */
  private async downloadDirect(url: string, customFilename: string): Promise<DownloadResult> {
    window.open(url, '_blank');
    return { 
      success: true, 
      method: 'direct', 
      size: 0,
      customFilename 
    };
  }

  /**
   * Trigger browser download by creating and clicking a temporary link
   * (Extracted from DownloadService.triggerDownload)
   */
  private triggerDownload(url: string, customFilename: string): void {
    const link = document.createElement('a');
    link.href = url;
    link.download = customFilename;
    link.style.display = 'none';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}

// Export singleton instance for easy consumption
export const clientDownloadService = new ClientDownloadService();
