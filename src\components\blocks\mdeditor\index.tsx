import React from "react";
import dynamic from "next/dynamic";

// 使用动态导入，并关闭服务器端渲染
const MDEditor = dynamic(() => import("@uiw/react-md-editor"), {
  ssr: false,
});

export default function MarkdownEditor({
  value,
  onChange,
}: {
  value: string;
  onChange: (value: string) => void;
}) {
  return (
    <div className="w-full md:w-[800px]">
      <MDEditor
        value={value}
        onChange={(val) => onChange(val || "")}
        height={600}
      />
    </div>
  );
}
