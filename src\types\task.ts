/**
 * Task Management Type Definitions
 *
 * @description
 * This file contains comprehensive type definitions for task management within the application.
 * It defines core task interfaces, status enums, and data structures used across the system.
 * 
 * Types are separated from the database model to allow safe client-side imports.
 */

// ==================== Core Enums & Constants ====================

/**
 * Task status constants for tasks
 * Tracks the lifecycle of task processing
 */
export const TaskStatus = {
  PENDING: 'pending',
  PROCESSING: 'processing',
  COMPLETED: 'completed',
  FAILED: 'failed'
} as const;

export type TaskStatusType = typeof TaskStatus[keyof typeof TaskStatus];

/**
 * Task types for AI generation
 * Defines the different categories of AI tasks supported
 */
export const TaskTypes = {
  IMAGE: 'image',
  TEXT: 'text',
  AUDIO: 'audio',
  VIDEO: 'video',
} as const;

export type TaskType = typeof TaskTypes[keyof typeof TaskTypes];

// ==================== Core Interfaces ====================

/**
 * Interface for text content item with optional metadata
 */
export interface TextItem {
  content: string;
  metadata?: {
    title?: string;
    format?: string;
    language?: string;
    wordCount?: number;
    [key: string]: unknown;
  };
}

/**
 * Task results interface - stores metadata and processing information
 * File URLs are managed through the files table separately
 */
export interface TaskResults {
  // Processing metadata
  generated_count?: number;
  processing_time_seconds?: number;
  model_version?: string;
  
  // Generation parameters used
  generation_params?: {
    prompt?: string;
    style?: string;
    quality?: string;
    size?: string;
    count?: number;
    [key: string]: unknown;
  };
  
  // Provider response data
  provider_response?: {
    provider_task_id?: string;
    credits_consumed?: number;
    model_info?: Record<string, unknown>;
    raw_response?: Record<string, unknown>;
  };
  
  // Text-specific results (for text generation tasks)
  text_results?: {
    contents?: TextItem[];
    total_words?: number;
    language?: string;
  };
}

/**
 * Task data interface for creating new tasks
 */
export interface TaskCreationData {
  task_id: string;
  user_uuid: string;
  task_type: string;
  status: string;
  params: any;
  model_id: string;
  provider: string;
  provider_task_id?: string;
}

/**
 * Complete task interface representing a task record from database
 * 
 * This interface extends TaskCreationData with additional fields 
 * that are present in stored tasks like timestamps and results.
 */
export interface Task {
  task_id: string;
  user_uuid: string;
  task_type: TaskType;
  status: TaskStatusType;
  params: Record<string, unknown>;
  model_id: string;
  provider: string;
  provider_task_id?: string | null;
  created_at: string;
  updated_at: string;
  completed_at?: string | null;
  progress?: number;
  results?: TaskResults;
  error?: string;
} 