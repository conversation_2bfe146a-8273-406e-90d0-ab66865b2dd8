import { AwsClient } from "aws4fetch";
import logger from "../logger";
import type {
  StorageConfig,
  PreviewUrlParams,
  DownloadUrlParams,
  UploadUrlParams,
  PresignedUploadResult,
  UploadResult,
  FileExistsResult,
  CloudStorageProvider,
} from './types';

/**
 * Cloudflare Workers Compatible Cloud Storage Implementation
 * Uses aws4fetch instead of AWS SDK to avoid Edge Runtime filesystem dependencies
 * Provides identical interface to cloud-storage-aws.ts for seamless switching
 * 
 * This implementation is optimized for Cloudflare Workers and Edge Runtime environments.
 * 
 * 🔑 KEY KNOWLEDGE POINTS:
 * 
 * 1. 🌐 URL FORMATS:
 *    - Virtual-hosted-style: https://bucket.endpoint.com/key (✅ Used here)
 *    - Path-style: https://endpoint.com/bucket/key (❌ Deprecated)
 * 
 * 2. 🎯 CUSTOM DOMAIN USAGE:
 *    - Preview/Download URLs: ✅ Can use custom domains (GET requests)
 *    - Upload URLs: ❌ Must use original R2 domain (PUT requests, signature validation)
 * 
 * 3. 🔐 CORS CONFIGURATION:
 *    - Upload operations require CORS on original R2 domain
 *    - Download/Preview can use CORS on custom domain
 * 
 * 4. 🔧 aws4fetch vs AWS SDK:
 *    - aws4fetch: Lightweight, Edge Runtime compatible (~25KB)
 *    - AWS SDK: Full featured, Node.js optimized (~580KB)
 *    - Both generate compatible presigned URLs
 * 
 * ===== AI ASSISTANT USAGE GUIDE =====
 * 
 * ❌ DO NOT import functions directly from this file!
 * 
 * This is an internal implementation file. Always import from the main entry point:
 * 
 * ```typescript
 * // ✅ CORRECT - Import from the main entry point
 * import { 
 *   createPresignedPreviewUrl,
 *   uploadFromBuffer,
 *   // ... other functions
 * } from '@/lib/cloud-storage';
 * 
 * // ❌ WRONG - Do NOT import from this file
 * import { ... } from '@/lib/cloud-storage/cloud-storage-cf';
 * ```
 * 
 * The main entry point will automatically select this implementation when
 * running in Cloudflare Workers/Edge Runtime or when DEPLOYMENT_TARGET=cloudflare.
 */

// ===== Types & Interfaces =====

// StorageConfig is now imported from './types'

// ===== Storage Client =====

function getAwsClient(config?: StorageConfig): AwsClient {
  return new AwsClient({
    accessKeyId: config?.accessKey || process.env.STORAGE_ACCESS_KEY || "",
    secretAccessKey: config?.secretKey || process.env.STORAGE_SECRET_KEY || "",
    region: config?.region || process.env.STORAGE_REGION || "auto",
    service: "s3",
  });
}

function getBucket(bucket?: string): string {
  const effectiveBucket = bucket || process.env.STORAGE_BUCKET || "";
  if (!effectiveBucket) {
    throw new Error("Bucket is required");
  }
  return effectiveBucket;
}

function getEndpoint(): string {
  const endpoint = process.env.STORAGE_ENDPOINT || "";
  if (!endpoint) {
    throw new Error("Storage endpoint is required");
  }
  return endpoint;
}

/**
 * Build Virtual-hosted-style URL for S3-compatible storage.
 * 
 * Virtual-hosted-style URLs (https://bucket.domain.com/key) are preferred over
 * Path-style URLs (https://domain.com/bucket/key) because:
 * 1. 🔧 AWS SDK COMPATIBILITY: Matches AWS SDK behavior
 * 2. 🌐 CORS HANDLING: Required for proper CORS configuration
 * 3. 📋 MODERN STANDARD: Path-style URLs are deprecated in AWS S3
 * 
 * @param endpoint - The storage endpoint URL
 * @param bucket - The bucket name
 * @param storageKey - The object key/path
 * @returns Virtual-hosted-style URL string
 */
function buildVirtualHostedUrl(endpoint: string, bucket: string, storageKey: string): string {
  const endpointUrl = new URL(endpoint);
  return `${endpointUrl.protocol}//${bucket}.${endpointUrl.hostname}${endpointUrl.pathname !== '/' ? endpointUrl.pathname : ''}/${storageKey}`;
}

// ===== Presigned URL Functions =====

/**
 * Create presigned URL for file preview (inline viewing in browser).
 * 
 * ✅ CUSTOM DOMAIN SUPPORT: Preview URLs can use custom domains because:
 * 1. 📖 READ-ONLY OPERATION: GET requests don't modify data, less security restrictions
 * 2. 🌐 DOMAIN FLEXIBILITY: Custom domains can proxy/redirect to original R2 URLs
 * 3. 🎨 USER EXPERIENCE: Custom domains provide better branding and caching
 * 
 * 🔧 VIRTUAL-HOSTED-STYLE URLS: This implementation generates Virtual-hosted-style URLs
 * (https://bucket.endpoint.com/key) instead of Path-style URLs (https://endpoint.com/bucket/key)
 * to match AWS SDK behavior and ensure proper CORS handling.
 * 
 * The function automatically replaces R2 domain with custom domain if configured.
 * Fallback: If custom domain replacement fails, original R2 URL is returned.
 */
export async function createPresignedPreviewUrl(params: PreviewUrlParams): Promise<string> {
  const aws = getAwsClient();
  const bucket = getBucket();
  const endpoint = getEndpoint();
  const expiresIn = params.expiresIn || 3600; // 1 hour default
  
  try {
    // Use Virtual-hosted-style URL format instead of Path-style
    // This matches AWS SDK behavior and is required for proper CORS handling
    // Virtual-hosted format: https://bucket.domain.com/key (✅)
    // Path format: https://domain.com/bucket/key (❌ deprecated)
    const virtualHostedUrl = buildVirtualHostedUrl(endpoint, bucket, params.storageKey);
    
    const url = new URL(virtualHostedUrl);
    url.searchParams.set('response-content-disposition', 'inline');
    
    const request = new Request(url.toString(), { method: 'GET' });
    const signedRequest = await aws.sign(request);
    
    let presignedUrl = signedRequest.url;
    
    // Replace R2 domain with custom domain if available
    if (process.env.STORAGE_DOMAIN && process.env.STORAGE_ENDPOINT) {
      try {
        const presignedUrlObj = new URL(presignedUrl);
        const customDomainUrlObj = new URL(process.env.STORAGE_DOMAIN);
        presignedUrlObj.hostname = customDomainUrlObj.hostname;
        presignedUrlObj.protocol = customDomainUrlObj.protocol;
        if (customDomainUrlObj.port) {
          presignedUrlObj.port = customDomainUrlObj.port;
        }
        presignedUrl = presignedUrlObj.toString();
      } catch (error) {
        logger.error("Failed to replace domain in presigned URL", error, {
          storageKey: params.storageKey,
        }, {
          filePath: "lib/cloud-storage-cf.ts",
          functionName: 'createPresignedPreviewUrl'
        });
      }
    }
    
    return presignedUrl;
  } catch (error) {
    logger.error(`Failed to create presigned preview URL for key ${params.storageKey}`, error, {
      storageKey: params.storageKey,
    }, {
      filePath: "lib/cloud-storage-cf.ts",
      functionName: 'createPresignedPreviewUrl'
    });
    throw new Error("Failed to generate presigned preview URL");
  }
}

/**
 * Create presigned URL for file download (force download with custom filename).
 * 
 * ✅ CUSTOM DOMAIN SUPPORT: Download URLs can use custom domains because:
 * 1. 📖 READ-ONLY OPERATION: GET requests don't modify data, less security restrictions
 * 2. 🌐 DOMAIN FLEXIBILITY: Custom domains can proxy/redirect to original R2 URLs
 * 3. 📁 FILENAME CONTROL: Custom domains maintain proper Content-Disposition headers
 * 
 * 🔧 VIRTUAL-HOSTED-STYLE URLS: This implementation generates Virtual-hosted-style URLs
 * (https://bucket.endpoint.com/key) instead of Path-style URLs (https://endpoint.com/bucket/key)
 * to match AWS SDK behavior and ensure proper CORS handling.
 * 
 * The function automatically replaces R2 domain with custom domain if configured.
 * Fallback: If custom domain replacement fails, original R2 URL is returned.
 */
export async function createPresignedDownloadUrl(params: DownloadUrlParams): Promise<string> {
  const aws = getAwsClient();
  const bucket = getBucket();
  const endpoint = getEndpoint();
  const expiresIn = params.expiresIn || 1800; // 30 minutes default
  
  try {
    // Use Virtual-hosted-style URL format instead of Path-style
    // This matches AWS SDK behavior and is required for proper CORS handling
    const virtualHostedUrl = buildVirtualHostedUrl(endpoint, bucket, params.storageKey);
    
    const url = new URL(virtualHostedUrl);
    
    // Generate proper Content-Disposition header with international support
    const generateContentDisposition = (filename: string): string => {
      const encodedFilename = encodeURIComponent(filename);
      return `attachment; filename*=UTF-8''${encodedFilename}`;
    };

    const disposition = params.customFilename
      ? generateContentDisposition(params.customFilename)
      : 'attachment';
    
    url.searchParams.set('response-content-disposition', disposition);
    
    const request = new Request(url.toString(), { method: 'GET' });
    const signedRequest = await aws.sign(request);
    
    let presignedUrl = signedRequest.url;
    
    // Replace R2 domain with custom domain if available
    if (process.env.STORAGE_DOMAIN && process.env.STORAGE_ENDPOINT) {
      try {
        const presignedUrlObj = new URL(presignedUrl);
        const customDomainUrlObj = new URL(process.env.STORAGE_DOMAIN);
        presignedUrlObj.hostname = customDomainUrlObj.hostname;
        presignedUrlObj.protocol = customDomainUrlObj.protocol;
        if (customDomainUrlObj.port) {
          presignedUrlObj.port = customDomainUrlObj.port;
        }
        presignedUrl = presignedUrlObj.toString();

        logger.info(
          `Presigned download URL created with custom domain`, {
            storageKey: params.storageKey,
            storageDomain: process.env.STORAGE_DOMAIN,
            storageEndpoint: process.env.STORAGE_ENDPOINT,
            presignedUrlWithCustomDomain: presignedUrl,
          }, {
            filePath: "lib/cloud-storage-cf.ts",
            functionName: 'createPresignedDownloadUrl'
          });
      } catch (error) {
        logger.error("Failed to replace domain in presigned URL", error, {
          storageKey: params.storageKey,
        }, {
          filePath: "lib/cloud-storage-cf.ts",
          functionName: 'createPresignedDownloadUrl'
        });
      }
    }

    logger.info(
      `Presigned download URL created`, {
        storageKey: params.storageKey,
      }, {
        filePath: "lib/cloud-storage-cf.ts",
        functionName: 'createPresignedDownloadUrl'
      });
    
    return presignedUrl;
  } catch (error) {
    logger.error(`Failed to create presigned download URL`, error, {
      storageKey: params.storageKey,
    }, {
      filePath: "lib/cloud-storage-cf.ts",
      functionName: 'createPresignedDownloadUrl'
    });
    throw new Error("Failed to generate presigned download URL");
  }
}

/**
 * Create a presigned URL for direct client-side uploading (PUT method).
 * 
 * ⚠️ IMPORTANT: Upload URLs vs Download/Preview URLs
 * 
 * Unlike preview/download URLs, presigned UPLOAD URLs CANNOT use custom domains.
 * This is a fundamental limitation of S3-compatible storage systems including Cloudflare R2.
 * 
 * Why Upload URLs Must Use Original Domain:
 * 1. 🔐 SIGNATURE VALIDATION: The AWS signature is cryptographically tied to the exact hostname
 * 2. 📋 S3 API REQUIREMENT: PUT operations require the original S3 endpoint for proper authentication
 * 3. 🌐 CORS CONFIGURATION: Upload CORS must be configured on the original R2 domain, not custom domain
 * 4. 🔧 aws4fetch LIMITATION: aws4fetch generates signatures tied to specific hostnames
 * 
 * URL Format Differences:
 * - Preview/Download: https://cdn.windflow.dev/path/to/file (✅ Custom domain OK)
 * - Upload:          https://bucket.account.r2.cloudflarestorage.com/path/to/file (❌ Original domain REQUIRED)
 * 
 * Virtual-hosted-style URL Format:
 * This implementation uses Virtual-hosted-style URLs (bucket.domain.com/key) instead of 
 * Path-style URLs (domain.com/bucket/key) to match AWS SDK behavior and ensure proper CORS handling.
 * 
 * aws4fetch Presigned URL Generation:
 * - Must add X-Amz-Expires parameter to URL before signing
 * - Must use { aws: { signQuery: true } } option to generate query string signature
 * - Without signQuery: true, aws4fetch only signs headers (not presigned URLs)
 * 
 * References:
 * - Cloudflare R2 Docs: "Presigned URLs can only be used with <accountid>.r2.cloudflarestorage.com"
 * - AWS S3 Docs: Presigned URLs require exact hostname match for signature validation
 * - aws4fetch Docs: Use signQuery: true for presigned URLs
 */
export async function createPresignedUploadUrl(params: UploadUrlParams): Promise<PresignedUploadResult> {
  const aws = getAwsClient();
  const bucket = getBucket();
  const endpoint = getEndpoint();
  const expiresIn = params.expiresIn || 3600; // 1 hour default
  
  try {
    // Calculate expiration time
    const expiresAt = new Date(Date.now() + expiresIn * 1000).toISOString();
    
    // Use Virtual-hosted-style URL format instead of Path-style
    // This matches AWS SDK behavior and is required for proper CORS handling
    const virtualHostedUrl = buildVirtualHostedUrl(endpoint, bucket, params.storageKey);
    
    // Create URL object and add expiration parameter for presigned URL
    const urlObj = new URL(virtualHostedUrl);
    urlObj.searchParams.set('X-Amz-Expires', expiresIn.toString());
    
    // Debug log to verify URL format
    logger.info('Creating presigned upload URL', {
      bucket,
      endpoint,
      storageKey: params.storageKey,
      virtualHostedUrl: urlObj.toString(),
      urlFormat: 'virtual-hosted-style',
      expiresIn
    }, {
      filePath: "lib/cloud-storage-cf.ts",
      functionName: 'createPresignedUploadUrl'
    });
    
    const request = new Request(urlObj.toString(), {
      method: 'PUT',
      headers: { 'Content-Type': params.mimeType },
    });

    // Use signQuery: true to generate presigned URL instead of Authorization header
    // This is the correct way to use aws4fetch for presigned URLs
    const signedRequest = await aws.sign(request, {
      aws: { signQuery: true }
    });

    // Debug log to verify final signed URL
    logger.info('Generated presigned upload URL', {
      presignedUrl: signedRequest.url,
      storageKey: params.storageKey
    }, {
      filePath: "lib/cloud-storage-cf.ts",
      functionName: 'createPresignedUploadUrl'
    });

    return {
      presignedUrl: signedRequest.url,
      fields: {}, // For PUT uploads, no form fields needed
      storageKey: params.storageKey,
      expiresAt,
    };
  } catch (error) {
    logger.error(`Failed to create presigned upload URL`, error, {
      storageKey: params.storageKey,
    }, {
      filePath: "lib/cloud-storage-cf.ts",
      functionName: 'createPresignedUploadUrl'
    });
    throw new Error("Failed to generate presigned upload URL");
  }
}

// ===== Direct Storage Operations =====

/**
 * Upload a file from a URL to storage.
 */
export async function uploadFromUrl(
  url: string, 
  storageKey: string, 
  contentType: string
): Promise<UploadResult> {
  const aws = getAwsClient();
  const bucket = getBucket();
  const endpoint = getEndpoint();
  
  const maxRetries = 3;
  const timeout = 30000; // 30 seconds timeout
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // Create AbortController for timeout handling
      const controller = new AbortController();
      const timeoutId = setTimeout(() => controller.abort(), timeout);
      
      try {
        logger.info(
          `Downloading from ${url}, attempt ${attempt}/${maxRetries}`, {
            url,
            attempt,
            maxRetries,
          }, {
            filePath: "lib/cloud-storage-cf.ts",
            functionName: 'uploadFromUrl'
          });
        
        // Fetch the file from the provided URL with timeout
        const response = await fetch(url, {
          signal: controller.signal,
          headers: {
            'User-Agent': 'STWD-Bot/1.0',
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive'
          },
          method: 'GET',
          redirect: 'follow',
          referrerPolicy: 'no-referrer'
        });
        
        clearTimeout(timeoutId);
        
        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status} ${response.statusText}`);
        }

        if (!response.body) {
          throw new Error("No body in response");
        }

        // Get the response as ArrayBuffer
        const arrayBuffer = await response.arrayBuffer();

        // Log more details about the downloaded file
        const actualContentType = response.headers.get('content-type') || contentType;
        const contentLength = response.headers.get('content-length');

        logger.info(
          `Successfully downloaded file from ${url} (${arrayBuffer.byteLength} bytes) on attempt ${attempt}`, {
            url,
            bufferLength: arrayBuffer.byteLength,
            actualContentType,
            contentLength,
            attempt,
          }, {
            filePath: "lib/cloud-storage-cf.ts",
            functionName: 'uploadFromUrl'
          });

        // Upload the file to storage using aws4fetch
        // Use Virtual-hosted-style URL format instead of Path-style
        const virtualHostedUploadUrl = buildVirtualHostedUrl(endpoint, bucket, storageKey);
        
        const uploadRequest = new Request(virtualHostedUploadUrl, {
          method: 'PUT',
          body: arrayBuffer,
          headers: {
            'Content-Type': actualContentType,
            'Content-Disposition': 'inline',
          },
        });

        const signedUploadRequest = await aws.sign(uploadRequest);
        const uploadResponse = await fetch(signedUploadRequest);

        if (!uploadResponse.ok) {
          throw new Error(`Upload failed: ${uploadResponse.status} ${uploadResponse.statusText}`);
        }

        const location = `${endpoint}/${bucket}/${storageKey}`;
        
        return {
          url: location,
          bucket: bucket,
          key: storageKey,
          filename: storageKey.split("/").pop(),
          fileSize: arrayBuffer.byteLength,
          customDomainUrl: process.env.STORAGE_DOMAIN
            ? `${process.env.STORAGE_DOMAIN}/${storageKey}`
            : location,
        };
        
      } finally {
        clearTimeout(timeoutId);
      }
      
    } catch (error: any) {
      logger.error(
        `Attempt ${attempt}/${maxRetries} failed for URL upload`, error, {
          url,
          storageKey,
          attempt,
        }, {
          filePath: "lib/cloud-storage-cf.ts",
          functionName: 'uploadFromUrl'
        });
      
      if (attempt === maxRetries) {
        throw new Error(`Failed to upload from URL after ${maxRetries} attempts: ${error.message}`);
      }
      
      // Wait before retrying (exponential backoff)
      const delay = Math.pow(2, attempt - 1) * 1000; // 1s, 2s, 4s...
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw new Error("Upload failed after all retries");
}

/**
 * Upload a file from a buffer to storage.
 */
export async function uploadFromBuffer(buffer: Buffer, storageKey: string, contentType: string): Promise<UploadResult> {
  const aws = getAwsClient();
  const bucket = getBucket();
  const endpoint = getEndpoint();
  
  try {
    // Upload the buffer to storage using aws4fetch
    // Use Virtual-hosted-style URL format instead of Path-style
    const virtualHostedUploadUrl = buildVirtualHostedUrl(endpoint, bucket, storageKey);
    
    const uploadRequest = new Request(virtualHostedUploadUrl, {
      method: 'PUT',
      body: buffer,
      headers: {
        'Content-Type': contentType,
        'Content-Disposition': 'inline',
      },
    });

    const signedUploadRequest = await aws.sign(uploadRequest);
    const uploadResponse = await fetch(signedUploadRequest);

    if (!uploadResponse.ok) {
      throw new Error(`Upload failed: ${uploadResponse.status} ${uploadResponse.statusText}`);
    }

    const location = `${endpoint}/${bucket}/${storageKey}`;

    return {
      url: location,
      bucket: bucket,
      key: storageKey,
      filename: storageKey.split("/").pop(),
      fileSize: buffer.length,
      customDomainUrl: process.env.STORAGE_DOMAIN
        ? `${process.env.STORAGE_DOMAIN}/${storageKey}`
        : location,
    };
  } catch (error) {
    logger.error(
      `Failed to upload buffer to storage at key ${storageKey}`, 
      error, {
        storageKey,
      }, {
        filePath: "lib/cloud-storage-cf.ts",
        functionName: 'uploadFromBuffer'
      });
    throw new Error("Failed to upload file from buffer");
  }
}

/**
 * Delete multiple files from storage.
 */
export async function deleteFiles(keys: string[]): Promise<boolean[]> {
  const aws = getAwsClient();
  const bucket = getBucket();
  const endpoint = getEndpoint();
  
  if (keys.length === 0) {
    logger.info("No files to delete");
    return [];
  }
  
  try {
    // Delete files one by one (aws4fetch doesn't support batch operations like AWS SDK)
    const deletePromises = keys.map(async (key) => {
      try {
        // Use Virtual-hosted-style URL format instead of Path-style
        const virtualHostedDeleteUrl = buildVirtualHostedUrl(endpoint, bucket, key);
        
        const deleteRequest = new Request(virtualHostedDeleteUrl, {
          method: 'DELETE',
        });

        const signedDeleteRequest = await aws.sign(deleteRequest);
        const deleteResponse = await fetch(signedDeleteRequest);

        if (!deleteResponse.ok && deleteResponse.status !== 404) {
          // 404 is okay - file already doesn't exist
          logger.error(`Failed to delete ${key}: ${deleteResponse.status} ${deleteResponse.statusText}`);
          return false;
        }
        return true;
      } catch (error) {
        logger.error(`Error deleting ${key}`, error);
        return false;
      }
    });

    const results = await Promise.all(deletePromises);
    const successCount = results.filter(Boolean).length;
    
    logger.info(
      `Successfully deleted ${successCount}/${keys.length} files from storage`, {
        deletedKeys: keys,
        results,
      }, {
        filePath: "lib/cloud-storage-cf.ts",
        functionName: 'deleteFiles'
      });
    
    return results;
  } catch (error) {
    logger.error(
      `Failed to delete files from storage`, error, {
        storageKeys: keys,
      }, {
        filePath: "lib/cloud-storage-cf.ts",
        functionName: 'deleteFiles'
      });
    return keys.map(() => false);
  }
}

// ===== Helper Functions =====

/**
 * Check if a URL belongs to our configured storage domain.
 */
export function isOwnStorageUrl(url: string): boolean {
  const storageDomain = process.env.STORAGE_DOMAIN;
  if (!storageDomain) return false;
  // Also check for the raw R2 domain for robustness
  return url.startsWith(storageDomain) || url.includes('.r2.cloudflarestorage.com');
}

/**
 * Verify if a file exists in cloud storage.
 */
export async function verifyFileExists(
  storageKey: string
): Promise<FileExistsResult> {
  try {
    const aws = getAwsClient();
    const bucket = getBucket();
    const endpoint = getEndpoint();
    
    // Use Virtual-hosted-style URL format instead of Path-style
    const virtualHostedHeadUrl = buildVirtualHostedUrl(endpoint, bucket, storageKey);
    
    const headRequest = new Request(virtualHostedHeadUrl, {
      method: 'HEAD',
    });

    const signedHeadRequest = await aws.sign(headRequest);
    const response = await fetch(signedHeadRequest);

    if (response.status === 404) {
      return { exists: false };
    }

    if (!response.ok) {
      throw new Error(`HEAD request failed: ${response.status} ${response.statusText}`);
    }

    return {
      exists: true,
      etag: response.headers.get('etag')?.replace(/"/g, ""), // Remove quotes from ETag
      size: parseInt(response.headers.get('content-length') || '0', 10),
    };
  } catch (error: any) {
    // If object doesn't exist, return exists: false
    if (error.message?.includes('404')) {
      return { exists: false };
    }
    
    logger.error(
      'Failed to verify file existence', 
      error, {
        storageKey,
      }, {
        filePath: "lib/cloud-storage-cf.ts",
        functionName: 'verifyFileExists'
      });
    return { exists: false };
  }
}
