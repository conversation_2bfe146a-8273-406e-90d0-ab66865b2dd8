CREATE TABLE "ai_tasks" (
	"id" serial PRIMARY KEY NOT NULL,
	"task_id" varchar(255) NOT NULL,
	"user_uuid" varchar(255) NOT NULL,
	"task_type" varchar(50) NOT NULL,
	"status" varchar(50) NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"updated_at" timestamp with time zone DEFAULT now() NOT NULL,
	"completed_at" timestamp with time zone,
	"params" jsonb NOT NULL,
	"results" jsonb,
	"error" text,
	"model_id" varchar(100) NOT NULL,
	"provider" varchar(50) NOT NULL,
	"provider_task_id" varchar(255),
	"progress" integer DEFAULT 0 NOT NULL,
	CONSTRAINT "ai_tasks_task_id_unique" UNIQUE("task_id")
);
--> statement-breakpoint
CREATE TABLE "files" (
	"id" serial PRIMARY KEY NOT NULL,
	"file_id" varchar(255) NOT NULL,
	"user_uuid" varchar(255) NOT NULL,
	"task_id" varchar(255),
	"storage_key" varchar(500) NOT NULL,
	"file_name" varchar(255) NOT NULL,
	"mime_type" varchar(100) NOT NULL,
	"file_size" bigint NOT NULL,
	"file_category" varchar(50) NOT NULL,
	"access_level" varchar(50) NOT NULL,
	"status" varchar(50) DEFAULT 'active' NOT NULL,
	"created_at" timestamp with time zone DEFAULT now() NOT NULL,
	"expires_at" timestamp with time zone,
	"metadata" jsonb,
	CONSTRAINT "files_file_id_unique" UNIQUE("file_id")
);
--> statement-breakpoint
CREATE INDEX "idx_ai_tasks_user_uuid" ON "ai_tasks" USING btree ("user_uuid");--> statement-breakpoint
CREATE INDEX "idx_ai_tasks_status" ON "ai_tasks" USING btree ("status");--> statement-breakpoint
CREATE INDEX "idx_ai_tasks_created_at" ON "ai_tasks" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "idx_ai_tasks_model_id" ON "ai_tasks" USING btree ("model_id");--> statement-breakpoint
CREATE INDEX "idx_files_user_uuid" ON "files" USING btree ("user_uuid");--> statement-breakpoint
CREATE INDEX "idx_files_task_id" ON "files" USING btree ("task_id");--> statement-breakpoint
CREATE INDEX "idx_files_status" ON "files" USING btree ("status");--> statement-breakpoint
CREATE INDEX "idx_files_file_category" ON "files" USING btree ("file_category");--> statement-breakpoint
CREATE INDEX "idx_files_access_level" ON "files" USING btree ("access_level");--> statement-breakpoint
CREATE INDEX "idx_files_created_at" ON "files" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "idx_files_expires_at" ON "files" USING btree ("expires_at");--> statement-breakpoint
CREATE INDEX "idx_files_storage_key" ON "files" USING btree ("storage_key");--> statement-breakpoint
CREATE INDEX "idx_files_user_category_status" ON "files" USING btree ("user_uuid","file_category","status");