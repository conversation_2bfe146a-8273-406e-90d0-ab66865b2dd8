services:
  dev:
    build:
      context: .
      dockerfile: Dockerfile.dev
    ports:
      - "3000:3000"
      - "8787:8787"  # wrangler dev port
    volumes:
      - .:/app
      - node_modules:/app/node_modules
      - wrangler_auth:/root/.wrangler
      - wrangler_cache:/root/.cache
    environment:
      - NODE_ENV=development
    env_file:
      - .env.docker
    working_dir: /app
    command: >
      sh -c "
        echo '🚀 Starting Docker development environment...' &&
        pnpm install &&
        echo '✅ Dependencies installed' &&
        echo '📝 Available commands:' &&
        echo '  - pnpm dev          (Next.js development server)' &&
        echo '  - pnpm cf:preview   (Cloudflare preview deployment)' &&
        echo '  - pnpm cf:deploy    (Cloudflare production deployment)' &&
        echo '  - wrangler tail     (View Cloudflare logs)' &&
        echo '💡 Run: docker-compose exec dev bash to enter container' &&
        tail -f /dev/null
      "

volumes:
  node_modules:
  wrangler_auth:
  wrangler_cache:
