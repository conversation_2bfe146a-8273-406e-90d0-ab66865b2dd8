---
description: Project directory structure and organization guidelines
globs: 
alwaysApply: true
---
# 目录结构规范

## 根目录结构

```text
├── src/                   # 源代码目录
│   ├── app/              # Next.js 15 App Router
│   │   ├── [locale]/     # 国际化路由
│   │   ├── api/          # API 路由
│   │   ├── (legal)/      # 法律相关页面分组
│   │   ├── globals.css   # 全局样式
│   │   └── theme.css     # 主题样式
│   ├── components/       # React 组件
│   │   ├── ui/           # 基础 UI 组件 (Shadcn UI)
│   │   ├── ai/           # AI 相关组件
│   │   ├── dashboard/    # 仪表板组件
│   │   └── blocks/       # 页面块组件
│   ├── db/               # 数据库相关
│   │   ├── schema.ts     # Drizzle-ORM 数据库模式
│   │   ├── config.ts     # Drizzle 配置
│   │   ├── index.ts      # 数据库连接
│   │   └── migrations/   # 数据库迁移文件
│   ├── models/           # 数据模型 (数据库操作层)
│   ├── services/         # 业务服务 (业务逻辑层)
│   ├── lib/              # 工具库和配置
│   ├── types/            # TypeScript 类型定义
│   ├── providers/        # Context 提供者
│   ├── hooks/            # 自定义 Hooks
│   ├── contexts/         # React Context
│   ├── i18n/             # 国际化配置
│   ├── auth/             # 认证相关
│   ├── aisdk/            # AI SDK 集成
│   └── middleware.ts     # Next.js 中间件
├── public/               # 静态资源
├── docs/                 # 项目文档
└── debug/                # 调试相关文件
```

## 数据库架构 (Drizzle-ORM)

- **src/db/schema.ts** - 数据库表结构定义
- **src/db/config.ts** - Drizzle-Kit 配置
- **src/db/index.ts** - 数据库连接和查询客户端
- **src/db/migrations/** - 自动生成的迁移文件

## 分层架构

- **Models层** (`src/models/`) - 数据访问层，直接与数据库交互
- **Services层** (`src/services/`) - 业务逻辑层，处理复杂业务逻辑
- **Components层** (`src/components/`) - 表现层，UI组件

## 组件组织原则

- **功能分组** - 按功能模块组织组件目录
- **层级清晰** - ui/ 存放基础组件，功能组件按模块分类
- **可复用性** - 通用组件放在 ui/ 目录，特定功能组件分模块存放

## 文件命名规范

- **组件文件** - 使用 PascalCase（如 `UserProfile.tsx`）
- **工具文件** - 使用 camelCase（如 `apiUtils.ts`）
- **配置文件** - 使用 kebab-case（如 `next.config.mjs`）
- **类型文件** - 使用 camelCase + .types.ts 后缀
- **数据库文件** - schema.ts, config.ts, index.ts

## 导入路径

- 使用 `@/` 别名指向 `src/` 目录
- 相对路径用于同级或子级文件
- 绝对路径用于跨模块引用

参考项目文件：[package.json](mdc:package.json)、[tsconfig.json](mdc:tsconfig.json)、[src/db/schema.ts](mdc:src/db/schema.ts)
