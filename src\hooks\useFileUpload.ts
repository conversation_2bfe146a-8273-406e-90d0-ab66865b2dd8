import { useState, useCallback } from 'react';
import { clientUploadService } from '@/lib/client-upload-service';
import { UploadResult } from '@/types/file-upload';

/**
 * Upload progress state for individual files
 */
interface FileUploadProgress {
  fileIndex: number;
  fileName: string;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  error?: string;
}

/**
 * Overall upload state
 */
interface UploadState {
  isUploading: boolean;
  progress: number; // Overall progress percentage (0-100)
  fileProgresses: FileUploadProgress[];
  error: string | null;
  results: UploadResult[];
}

/**
 * Return type for the useFileUpload hook
 */
interface UseFileUploadReturn {
  uploadState: UploadState;
  uploadFiles: (files: File[], userUuid?: string) => Promise<UploadResult[]>;
  // uploadFile: (file: File, options: UploadOptions) => Promise<UploadResult>;
  reset: () => void;
  isUploading: boolean;
  progress: number;
  error: string | null;
  results: UploadResult[];
}

/**
 * Custom hook for file upload functionality
 * Provides state management and upload methods for file uploads
 * 
 * @returns Object containing upload state and methods
 */
export function useFileUpload(): UseFileUploadReturn {
  const [uploadState, setUploadState] = useState<UploadState>({
    isUploading: false,
    progress: 0,
    fileProgresses: [],
    error: null,
    results: [],
  });

  /**
   * Reset upload state to initial values
   */
  const reset = useCallback(() => {
    setUploadState({
      isUploading: false,
      progress: 0,
      fileProgresses: [],
      error: null,
      results: [],
    });
  }, []);

  /**
   * Update progress for a specific file
   */
  const updateFileProgress = useCallback((
    fileIndex: number,
    progress: number,
    status: FileUploadProgress['status'],
    error?: string
  ) => {
    setUploadState(prev => ({
      ...prev,
      fileProgresses: prev.fileProgresses.map((fp, index) =>
        index === fileIndex
          ? { ...fp, progress, status, error }
          : fp
      ),
      // Calculate overall progress
      progress: Math.round(
        prev.fileProgresses.reduce((sum, fp, index) => {
          const currentProgress = index === fileIndex ? progress : fp.progress;
          return sum + currentProgress;
        }, 0) / prev.fileProgresses.length
      ),
    }));
  }, []);

  /**
   * Upload multiple files with progress tracking
   * @param files Array of File objects to upload
   * @param userUuid User UUID for authentication
   * @returns Promise resolving to array of upload results
   */
  const uploadFiles = useCallback(async (
    files: File[],
    userUuid?: string
): Promise<UploadResult[]> => {
    if (files.length === 0) {
      throw new Error('No files provided for upload');
    }

    // Initialize upload state
    setUploadState({
      isUploading: true,
      progress: 0,
      fileProgresses: files.map((file, index) => ({
        fileIndex: index,
        fileName: file.name,
        progress: 0,
        status: 'pending',
      })),
      error: null,
      results: [],
    });

    try {
      // Start uploading files
      setUploadState(prev => ({
        ...prev,
        fileProgresses: prev.fileProgresses.map(fp => ({
          ...fp,
          status: 'uploading',
        })),
      }));

      // Use the client upload service to handle the upload
      const results = await clientUploadService.uploadFiles(files, userUuid);

      // Update final state based on results
      setUploadState(prev => ({
        ...prev,
        isUploading: false,
        progress: 100,
        fileProgresses: results.map((result, index) => ({
          fileIndex: index,
          fileName: result.fileName,
          progress: 100,
          status: result.success ? 'success' : 'error',
          error: result.error,
        })),
        results,
        error: null,
      }));

      return results;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      
      setUploadState(prev => ({
        ...prev,
        isUploading: false,
        error: errorMessage,
        fileProgresses: prev.fileProgresses.map(fp => ({
          ...fp,
          status: 'error',
          error: errorMessage,
        })),
      }));

      throw error;
    }
  }, []);

  /**
   * Upload a single file (convenience method)
   * @param file File object to upload
   * @param options Upload options
   * @returns Promise resolving to upload result
   */
  // const uploadFile = useCallback(async (
  //   file: File,
  //   options: UploadOptions
  // ): Promise<UploadResult> => {
  //   const results = await uploadFiles([file], options);
  //   return results[0];
  // }, [uploadFiles]);

  return {
    uploadState,
    uploadFiles,
    // uploadFile,
    reset,
    // Convenience accessors for common state properties
    isUploading: uploadState.isUploading,
    progress: uploadState.progress,
    error: uploadState.error,
    results: uploadState.results,
  };
}
