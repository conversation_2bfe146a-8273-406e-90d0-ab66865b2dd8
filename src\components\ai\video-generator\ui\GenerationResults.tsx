"use client";

import { useTranslations } from 'next-intl';
import { useState, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { Ta<PERSON>, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { GenerationStatus } from '@/types/ai/common';
import { Download, ExternalLink, Loader2, RefreshCw } from 'lucide-react';
import { cn } from "@/lib/utils";
import { useVideoDownload } from '@/hooks/useDownload';

// Simple progress component defined locally
function Progress({ value = 0, className }: { value?: number; className?: string }) {
  const clampedValue = Math.min(Math.max(value, 0), 100);
  
  return (
    <div
      className={cn(
        "relative h-2 w-full overflow-hidden rounded-full bg-secondary",
        className
      )}
    >
      <div
        className="h-full w-full flex-1 bg-primary transition-all"
        style={{ transform: `translateX(-${100 - clampedValue}%)` }}
      />
    </div>
  );
}

interface GenerationResultsProps {
  status: GenerationStatus;
  onRetry?: () => void;
  setResultsRef: (el: HTMLDivElement | null) => void;
  className?: string;
}

export default function GenerationResults({
  status,
  onRetry,
  setResultsRef,
  className = '',
}: GenerationResultsProps) {
  const t = useTranslations('components.video_generator');
  const [activeVideoIndex, setActiveVideoIndex] = useState(0);
  
  // Use simplified download hook for video downloads
  const { downloadVideo, isLoading, error, clearError } = useVideoDownload();
  
  // Simplified download handler using fileId and fileName
  const handleDownloadVideo = async (fileId: string, fileName: string) => {
    try {
      console.log(`Downloading video with fileId: ${fileId}`);
      await downloadVideo(fileId, fileName);
    } catch (error) {
      console.error('Video download failed:', error);
      // Error is already handled by the hook
    }
  };

  // Handle opening video in new tab
  const handleViewOriginalVideo = (videoUrl: string) => {
    window.open(videoUrl, '_blank');
  };

  // Render the component
  return (
    <div 
      className={cn("space-y-6", className)} 
      ref={setResultsRef}
    >
      {/* Loading state */}
      {status.isGenerating && (
        <Card className="p-6">
          <div className="flex items-center space-x-3 mb-4">
            <Loader2 className="h-5 w-5 animate-spin text-blue-500" />
            <span className="text-lg font-medium">
              {t('generation_results.generating', { fallback: 'Generating your video...' })}
            </span>
          </div>
          
          <div className="space-y-3">
            <Progress value={status.progress} className="w-full" />
            <p className="text-sm text-muted-foreground text-center">
              {status.progress.toFixed(0)}% {t('generation_results.complete', { fallback: 'complete' })}
            </p>
          </div>
        </Card>
      )}

      {/* Error state */}
      {status.error && (
        <Card className="p-6 border-destructive bg-destructive/5">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <div className="h-5 w-5 text-destructive">⚠️</div>
            </div>
            <div className="flex-grow">
              <h3 className="text-lg font-medium text-destructive mb-2">
                {t('generation_results.error_title', { fallback: 'Generation Failed' })}
              </h3>
              <p className="text-sm text-destructive mb-4">
                {status.error}
              </p>
              {onRetry && (
                <Button 
                  onClick={onRetry} 
                  variant="outline" 
                  size="sm"
                  className="gap-2"
                >
                  <RefreshCw className="h-4 w-4" />
                  {t('generation_results.retry', { fallback: 'Try Again' })}
                </Button>
              )}
            </div>
          </div>
        </Card>
      )}

      {/* Success state with results */}
      {!status.isGenerating && !status.error && status.files && status.files.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-xl font-semibold">
            {t('generation_results.title', { fallback: 'Generated Videos' })}
          </h3>
          
          {/* Multiple videos - show tabs */}
          {status.files.length > 1 ? (
            <Tabs value={activeVideoIndex.toString()} onValueChange={(value) => setActiveVideoIndex(parseInt(value))}>
              <TabsList className="w-full">
                {status.files.map((_, index) => (
                  <TabsTrigger key={index} value={index.toString()} className="flex-1">
                    {t('generation_results.video_tab', { 
                      fallback: 'Video {{number}}', 
                      number: index + 1 
                    })}
                  </TabsTrigger>
                ))}
              </TabsList>
              
              {status.files.map((file, index) => {
                return (
                  <TabsContent key={index} value={index.toString()} className="mt-0">
                    <VideoResult
                      videoUrl={file.url}
                      onDownload={() => handleDownloadVideo(file.fileId, file.fileName)}
                      onViewOriginal={() => handleViewOriginalVideo(file.url)}
                      isDownloading={isLoading}
                      downloadError={error}
                    />
                  </TabsContent>
                );
              })}
            </Tabs>
          ) : (
            // Single video view
            (() => {
              const file = status.files?.[0];
              if (!file) return null;
              return (
                <VideoResult
                  videoUrl={file.url}
                  onDownload={() => handleDownloadVideo(file.fileId, file.fileName)}
                  onViewOriginal={() => handleViewOriginalVideo(file.url)}
                  isDownloading={isLoading}
                  downloadError={error}
                />
              );
            })()
          )}
        </div>
      )}
    </div>
  );
}

// Video result component for displaying individual videos
function VideoResult({
  videoUrl,
  onDownload,
  onViewOriginal,
  isDownloading = false,
  downloadError,
}: {
  videoUrl: string;
  onDownload: () => void;
  onViewOriginal: () => void;
  isDownloading?: boolean;
  downloadError?: string | null;
}) {
  const t = useTranslations('components.video_generator');

  return (
    <div className="w-full">
      <div className="relative w-full bg-muted rounded-lg overflow-hidden aspect-video mb-4">
        <video 
          src={videoUrl} 
          className="w-full h-full object-contain" 
          controls
          autoPlay
          loop
          muted
          playsInline
        />
      </div>
      
      {/* Download error display */}
      {downloadError && (
        <div className="mb-4">
          <div className="flex items-center gap-2 mb-2 text-destructive text-sm">
            <span>{downloadError}</span>
          </div>
        </div>
      )}
      
      <div className="flex flex-wrap gap-2 justify-center">
        <Button 
          onClick={onDownload} 
          variant="secondary" 
          className="gap-1"
          disabled={isDownloading}
        >
          {isDownloading ? (
            <Loader2 className="h-4 w-4 mr-1 animate-spin" />
          ) : (
            <Download className="h-4 w-4 mr-1" />
          )}
          {isDownloading 
            ? t('generation_results.downloading', { fallback: 'Downloading...' })
            : t('generation_results.download', { fallback: 'Download Video' })
          }
        </Button>
        <Button onClick={onViewOriginal} variant="outline" className="gap-1">
          <ExternalLink className="h-4 w-4 mr-1" />
          {t('generation_results.view_original', { fallback: 'View in New Tab' })}
        </Button>
      </div>
    </div>
  );
} 