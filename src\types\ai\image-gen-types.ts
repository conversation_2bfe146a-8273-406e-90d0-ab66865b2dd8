// Available image generation modes
export enum ImageGenerationMode {
  TextToImage = 'textToImage',
  ImageToImage = 'imageToImage',
  BatchGeneration = 'batchGeneration',
  ImageFusion = 'imageFusion',
}

// Style options for image generation
export enum ImageStyle {
  NoStyle = 'No Style',
  SnoopyStyle = 'Snoopy Style',
  IrasutoyaIllustrations = 'Irasutoya Illustrations',
  ChibiEmojiStickers = 'Chibi Emoji Stickers',
  FourGridComics = '4-Grid Comics',
  GhibliStyle = 'Ghibli Style',
  AnimeStyle = 'Anime Style',
  PixelArt = 'Pixel Art',
  DisneyStyle = 'Disney Style',
  PixarStyle = 'Pixar Style',
  RealisticStyle = 'Realistic Style',
}

// Aspect ratio options
export enum AspectRatio {
  Square = 'Square',
  Landscape = 'Landscape',
  Portrait = 'Portrait',
}

// Color options for image generation
export enum ColorOption {
  NoColor = 'No Color',
  WarmColors = 'Warm Colors',
  ColdColors = 'Cold Colors',
  SoftColors = 'Soft Colors',
  VibrantColors = 'Vibrant Colors',
  PastelColors = 'Pastel Colors',
  BlackAndWhite = 'Black And White',
}

// Composition options for image generation
export enum CompositionOption {
  NoComposition = 'No Composition',
  BlurryBackground = 'Blurry Background',
  CloseUp = 'Close-up',
  WideAngle = 'Wide Angle',
  DepthOfField = 'Depth of Field',
  LowAngle = 'Low Angle',
  HighAngle = 'High Angle',
  MacroPhotography = 'Macro Photography',
}

/**
 * Frontend request format directly matching the form UI
 */
export interface ImageGenerationRequest {
  mode: ImageGenerationMode;
  prompt: string;

  // 反向提示词
  negativePrompt?: string;

  imageStyle?: ImageStyle;
  aspectRatio?: AspectRatio;
  colorOption?: ColorOption;
  compositionOption?: CompositionOption;
  // modelName: string;
  outputCount: number;
  // outputFormat: DefaultImageExtension;
  
  /**
   * Array of uploaded file IDs from presigned URL uploads
   * Used in image processing modes like ImageToImage and ImageFusion
   */
  fileIds?: string[];
}
