# AI Image Generator Component

A React component that provides text-to-image and image-to-image generation capabilities through a clean, user-friendly interface. This component follows clean architecture principles with clear separation of frontend and backend concerns.

## Architecture

The image generator follows a clean architecture pattern:

```
Frontend (React)                      Backend (Next.js API)
+---------------------+              +--------------------+
|                     |  HTTP API    |                    |
|  ImageGenerator     +------------->+  API Route         |
|  Component          |              |  (Controller)      |
|                     |              |                    |
+---------------------+              +----------+---------+
         |                                      |
         |                                      |
         v                                      v
+---------------------+              +--------------------+
|                     |              |                    |
|  UI Components      |              |  Backend Service   |
|  - PromptInput      |              |  (Business Logic)  |
|  - ImageUploader    |              |                    |
|  - StyleSelector    |              |                    |
|  - GenerationResults|              |                    |
+---------------------+              +----------+---------+
                                                |
                                                |
                                                v
                                     +--------------------+
                                     |                    |
                                     |  External AI APIs  |
                                     |  - Replicate       |
                                     |  - OpenAI          |
                                     |  - StabilityAI     |
                                     +--------------------+
```

### Key Components

1. **Frontend**
   - `ImageGenerator`: Main component that handles UI state and API communication
   - UI subcomponents: `PromptInput`, `ImageUploader`, `StyleSelector`, `GenerationResults`
   - `ImageGenerationClientUtil`: Client-side utilities for image handling

2. **Backend**
   - API route: Controller that receives requests and delegates to the service
   - `ImageGenerationBackendService`: Core business logic service
   - Model access: Configuration for different AI models and providers

## Usage

### Basic Usage

```tsx
import ImageGenerator from '@/components/ai/image-generator';
import { ImageGenerationMode } from '@/types/ai/image-generator';

export default function GeneratorPage() {
  return (
    <div className="container">
      <ImageGenerator 
        initialMode={ImageGenerationMode.TextToImage}
        initialModel="flux-schnell"
        onImagesGenerated={(images) => console.log('Generated:', images)}
      />
    </div>
  );
}
```

### Props

| Property | Type | Description |
|----------|------|-------------|
| `initialMode` | `ImageGenerationMode` | Starting generation mode (TextToImage, ImageToImage) |
| `initialModel` | `string` | Default model to use (must be defined in AI_MODELS) |
| `className` | `string` | Optional CSS class to apply to the container |
| `onImagesGenerated` | `(images: GeneratedImage[]) => void` | Optional callback when images are generated |

## Supported Modes

1. **Text to Image**: Generate images from text prompts
2. **Image to Image**: Modify existing images using text prompts
3. **Batch Generation**: (Coming soon) Apply the same prompt to multiple images

## Environment Variables

The backend service requires these environment variables:

- `REPLICATE_API_TOKEN`: API token for Replicate
- `OPENAI_API_KEY`: API key for OpenAI (for DALL-E models)

## Adding New Models

To add new image generation models:

1. Update the `AI_MODELS` constant in `types/ai/image-generator.ts`
2. If adding a new provider, implement the provider-specific logic in `ImageGenerationBackendService`

## Best Practices

1. **Separation of Concerns**
   - Keep UI logic in the frontend components
   - Keep business logic in the backend service
   - Use the API route as a thin controller

2. **Error Handling**
   - Frontend validates input before sending to API
   - Backend performs thorough validation
   - Proper error messages are displayed to users

3. **Performance**
   - Use proper loading states and progress indicators
   - Implement request timeout handling
   - Consider caching for frequently used models/styles

4. **Security**
   - Never expose API tokens to the frontend
   - Validate all user inputs on the server
   - Implement rate limiting for production use

## Internationalization

The component fully supports i18n through the `next-intl` package. All user-facing text is localized via translation keys in:

- `i18n/messages/en.json` (English)
- `i18n/messages/zh.json` (Chinese)

## Future Improvements

- [ ] Add support for StabilityAI provider
- [ ] Implement batch generation mode
- [ ] Add more customization options
- [ ] Improve error handling and recovery
- [ ] Add image editing features 