/**
 * Cloud Storage Interface Definitions
 * 
 * This file defines the unified interface that all cloud storage implementations
 * must follow, ensuring type safety and API consistency across different platforms.
 * 
 * ===== AI ASSISTANT USAGE GUIDE =====
 * 
 * ❌ DO NOT import types directly from this file!
 * 
 * Instead, import all types from the main entry point:
 * 
 * ```typescript
 * // ✅ CORRECT - Import from the main entry point
 * import type { 
 *   StorageConfig,
 *   PresignedUploadResult,
 *   UploadResult,
 *   FileExistsResult,
 * } from '@/lib/cloud-storage';
 * 
 * // ❌ WRONG - Do NOT import from this file
 * import type { ... } from '@/lib/cloud-storage/types';
 * ```
 * 
 * This file is for internal use only and should not be imported directly.
 */

// ===== Configuration Types =====

export interface StorageConfig {
  endpoint: string;
  region: string;
  accessKey: string;
  secretKey: string;
}

// ===== Function Parameter Types =====

export interface PreviewUrlParams {
  storageKey: string;
  expiresIn?: number;
}

export interface DownloadUrlParams {
  storageKey: string;
  customFilename?: string;
  expiresIn?: number;
}

export interface UploadUrlParams {
  storageKey: string;
  mimeType: string;
  expiresIn?: number;
}

// ===== Return Types =====

export interface PresignedUploadResult {
  presignedUrl: string;
  fields: Record<string, string>;
  storageKey: string;
  expiresAt: string;
}

export interface UploadResult {
  url: string;
  bucket: string;
  key: string;
  filename?: string;
  fileSize: number;
  customDomainUrl?: string;
}

export interface FileExistsResult {
  exists: boolean;
  size?: number;
  etag?: string;
}

// ===== Main Interface =====

/**
 * Cloud Storage Provider Interface
 * 
 * All cloud storage implementations must implement this interface to ensure
 * consistent API across different platforms (AWS SDK, aws4fetch, etc.)
 */
export interface CloudStorageProvider {
  /**
   * Create presigned URL for file preview (inline viewing in browser)
   */
  createPresignedPreviewUrl(params: PreviewUrlParams): Promise<string>;

  /**
   * Create presigned URL for file download (force download with custom filename)
   */
  createPresignedDownloadUrl(params: DownloadUrlParams): Promise<string>;

  /**
   * Create presigned URL for direct client-side uploading
   */
  createPresignedUploadUrl(params: UploadUrlParams): Promise<PresignedUploadResult>;

  /**
   * Upload file from URL to storage
   */
  uploadFromUrl(url: string, storageKey: string, contentType: string): Promise<UploadResult>;

  /**
   * Upload file from buffer to storage
   */
  uploadFromBuffer(buffer: Buffer, storageKey: string, contentType: string): Promise<UploadResult>;

  /**
   * Delete multiple files from storage
   */
  deleteFiles(keys: string[]): Promise<boolean[]>;

  /**
   * Check if URL belongs to our storage
   */
  isOwnStorageUrl(url: string): boolean;

  /**
   * Verify if file exists in storage
   */
  verifyFileExists(storageKey: string): Promise<FileExistsResult>;
} 