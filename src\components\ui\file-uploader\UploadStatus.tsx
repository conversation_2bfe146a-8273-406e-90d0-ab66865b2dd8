"use client";

import { useTranslations } from 'next-intl';
import { CheckCircle, XCircle, Loader2, AlertCircle } from "lucide-react";
import { cn } from "@/lib/utils";
import { Alert, AlertDescription } from "@/components/ui/alert";

interface UploadStatusProps {
  progress?: number;
  message?: string;
  error?: string | null;
  isUploading?: boolean;
  isComplete?: boolean;
  className?: string;
}

/**
 * Upload status indicator component
 * Shows progress, success, error, and loading states
 */
export function UploadStatus({
  progress = 0,
  message,
  error,
  isUploading = false,
  isComplete = false,
  className,
}: UploadStatusProps) {
  const t = useTranslations('common.file_uploader');
  
  // Don't render anything if there's nothing to show
  if (!isUploading && !isComplete && !error && !message) {
    return null;
  }

  return (
    <div className={cn("space-y-3", className)}>
      {/* Progress bar and status */}
      {(isUploading || isComplete) && (
        <div className="space-y-2">
          {/* Status icon and message */}
          <div className="flex items-center space-x-2">
            {isUploading && (
              <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
            )}
            {isComplete && !error && (
              <CheckCircle className="h-4 w-4 text-green-500" />
            )}
            {error && (
              <XCircle className="h-4 w-4 text-red-500" />
            )}
            
            <span className={cn(
              "text-sm font-medium",
              {
                "text-blue-600 dark:text-blue-400": isUploading,
                "text-green-600 dark:text-green-400": isComplete && !error,
                "text-red-600 dark:text-red-400": error,
                "text-foreground": !isUploading && !isComplete && !error,
              }
            )}>
              {error ? t('upload_failed', { fallback: '上传失败' }) : 
               isComplete ? t('upload_complete', { fallback: '上传完成' }) : 
               message || t('uploading', { fallback: '正在上传...' })}
            </span>
          </div>

          {/* Progress bar */}
          {isUploading && (
            <div className="space-y-1">
              <div className="w-full bg-secondary rounded-full h-2 overflow-hidden">
                <div 
                  className="h-full bg-primary transition-all duration-300 ease-out"
                  style={{ width: `${Math.min(100, Math.max(0, progress))}%` }}
                />
              </div>
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>{t('progress', { fallback: '进度' })}</span>
                <span>{progress}%</span>
              </div>
            </div>
          )}
        </div>
      )}

      {/* Error message */}
      {error && (
        <Alert variant="destructive" className="border-red-200 dark:border-red-800">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-sm">
            {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Success message with details */}
      {isComplete && !error && message && (
        <Alert className="border-green-200 dark:border-green-800 bg-green-50 dark:bg-green-950/20">
          <CheckCircle className="h-4 w-4 text-green-600" />
          <AlertDescription className="text-sm text-green-700 dark:text-green-300">
            {message}
          </AlertDescription>
        </Alert>
      )}
    </div>
  );
} 