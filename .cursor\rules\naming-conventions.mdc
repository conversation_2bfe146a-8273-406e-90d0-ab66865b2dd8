---
description: Naming conventions for files, variables, and components
globs: 
alwaysApply: false
---
# 命名规范

## 文件和目录命名
- **组件文件** - PascalCase（如 `UserProfile.tsx`、`ChatMessage.tsx`）
- **页面文件** - kebab-case（如 `page.tsx`、`layout.tsx`、`loading.tsx`）
- **工具文件** - camelCase（如 `apiClient.ts`、`formatUtils.ts`）
- **配置文件** - kebab-case（如 `next.config.mjs`、`tailwind.config.ts`）
- **目录名** - kebab-case（如 `user-profile/`、`ai-chat/`）

## 变量和函数命名
- **变量** - camelCase（如 `userName`、`isLoading`、`chatMessages`）
- **函数** - camelCase（如 `getUserData`、`handleSubmit`、`formatDate`）
- **常量** - SCREAMING_SNAKE_CASE（如 `API_BASE_URL`、`MAX_RETRY_COUNT`）
- **类型/接口** - PascalCase（如 `UserProfile`、`ChatMessage`、`ApiResponse`）

## React 组件规范
- **组件名** - PascalCase，描述性强（如 `ChatMessageList`、`UserAvatarDropdown`）
- **Props 接口** - 组件名 + Props（如 `ChatMessageProps`、`UserAvatarProps`）
- **Hook 函数** - use + 功能描述（如 `useUserData`、`useChatMessages`）
- **Event Handler** - handle + 动作（如 `handleClick`、`handleSubmit`、`handleChange`）

## API 和数据库命名
- **API 路由** - RESTful 风格，kebab-case（如 `/api/user-profile`、`/api/chat-messages`）
- **数据库表** - snake_case（如 `user_profiles`、`chat_messages`）
- **字段名** - snake_case（如 `created_at`、`user_id`、`message_content`）

## 特殊约定
- **布尔值** - 使用 is/has/can 前缀（如 `isVisible`、`hasPermission`、`canEdit`）
- **数组** - 使用复数形式（如 `users`、`messages`、`items`）
- **ID 字段** - 统一使用 id 后缀（如 `userId`、`messageId`、`chatId`）

