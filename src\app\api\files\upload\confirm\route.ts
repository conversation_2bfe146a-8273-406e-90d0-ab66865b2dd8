import { NextRequest, NextResponse } from "next/server";
import { confirmBatchUpload } from "@/services/file";
import { nextRespData, nextRespErr } from "@/lib/resp";
import logger from "@/lib/logger";
import {
  ConfirmUploadRequest,
  ConfirmUploadResponse,
} from "@/types/file-upload";

/**
 * Confirm successful uploads and activate file records
 * Updates file status from pending to active after successful cloud upload
 * 
 * POST /api/files/upload/confirm
 * 
 * Request body:
 * {
 *   fileIds: ["file_uuid_1", "file_uuid_2", ...]
 * }
 * 
 * Response:
 * {
 *   code: 0,
 *   message: "Upload confirmed successfully",
 *   data: {
 *     confirmedFiles: [
 *       {
 *         fileId: "file_uuid_1",
 *         fileName: "image.jpg",
 *         fileSize: 1024000,
 *         mimeType: "image/jpeg"
 *       }
 *     ]
 *   }
 * }
 */
export async function POST(req: NextRequest) {
  try {
    // Extract user information from headers
    const userUuid = req.headers.get('x-user-uuid');
    if (!userUuid) {
      return nextRespErr("User authentication required");
    }

    // Parse and validate request body
    let requestBody: ConfirmUploadRequest;
    try {
      requestBody = await req.json();
    } catch (error) {
      return nextRespErr("Invalid JSON in request body");
    }

    const { fileIds } = requestBody;

    // Validate required fields
    if (!fileIds || !Array.isArray(fileIds) || fileIds.length === 0) {
      return nextRespErr("File IDs array is required and cannot be empty");
    }

    if (fileIds.length > 10) {
      return nextRespErr("Maximum 10 files per batch confirmation");
    }

    // Validate each file ID
    for (let i = 0; i < fileIds.length; i++) {
      const fileId = fileIds[i];
      
      if (!fileId || typeof fileId !== 'string') {
        return nextRespErr(`File ID ${i + 1}: must be a non-empty string`);
      }
    }

    logger.info('Processing upload confirmation request', {
      userUuid,
      fileIds,
      fileCount: fileIds.length
    }, { filePath: "app/api/files/upload/confirm/route.ts", functionName: 'POST' });

    // Confirm batch upload
    const confirmedFiles = await confirmBatchUpload(fileIds, userUuid);

    const response: ConfirmUploadResponse = {
      confirmedFiles,
    };

    logger.info('Successfully confirmed batch upload', {
      userUuid,
      confirmedCount: confirmedFiles.length,
      totalRequested: fileIds.length,
      confirmedFileIds: confirmedFiles.map(f => f.fileId)
    }, { filePath: "app/api/files/upload/confirm/route.ts", functionName: 'POST' });

    return nextRespData(response);
  } catch (error) {
    logger.error('Failed to confirm batch upload', error, {
      userUuid: req.headers.get('x-user-uuid')
    }, { filePath: "app/api/files/upload/confirm/route.ts", functionName: 'POST' });
    
    return nextRespErr("Failed to confirm upload");
  }
} 